<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险指标配置 - 微邮付商户健康度监测系统</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 24px;
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .form-header {
            padding: 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .form-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .form-content {
            padding: 24px;
        }

        .form-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color-light);
        }

        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-group {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group.full-width {
            flex: none;
            width: 100%;
        }

        .form-label {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .form-label.required::after {
            content: ' *';
            color: var(--error-red);
        }

        .form-input, .form-select, .form-textarea {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-container);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .radio-group {
            display: flex;
            gap: 16px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .radio-item input[type="radio"] {
            width: 16px;
            height: 16px;
        }

        .threshold-config {
            background: var(--bg-layout);
            border-radius: 6px;
            padding: 16px;
            border: 1px solid var(--border-color-light);
        }

        .threshold-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .threshold-item:last-child {
            margin-bottom: 0;
        }

        .threshold-label {
            min-width: 60px;
            font-weight: 500;
        }

        .threshold-label.high-risk {
            color: var(--error-red);
        }

        .threshold-label.medium-risk {
            color: var(--warning-orange);
        }

        .threshold-label.low-risk {
            color: var(--success-green);
        }

        .threshold-input {
            width: 100px;
        }

        .form-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: var(--bg-layout);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .btn-default:hover {
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .help-text {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        .error-text {
            font-size: 12px;
            color: var(--error-red);
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <!-- 表单头部 -->
        <div class="form-header">
            <h1 class="form-title" id="formTitle">新增风险指标</h1>
            <button class="btn btn-default" onclick="window.close()">
                <i class="fas fa-times"></i>
                关闭
            </button>
        </div>

        <!-- 表单内容 -->
        <div class="form-content">
            <form id="indicatorForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h2 class="section-title">基本信息</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">指标名称</label>
                            <input type="text" class="form-input" id="indicatorName" placeholder="请输入指标名称" required>
                            <div class="help-text">指标的业务名称，如"短时多频进件"</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">指标编码</label>
                            <input type="text" class="form-input" id="indicatorCode" placeholder="请输入指标编码" required>
                            <div class="help-text">系统唯一标识，如RISK_001</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">指标分类</label>
                            <select class="form-select" id="category" required>
                                <option value="">请选择分类</option>
                                <option value="PROFILE">资料风险</option>
                                <option value="TRANSACTION">交易风险</option>
                                <option value="SUBSIDY">补贴风险</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <div class="radio-group">
                                <div class="radio-item">
                                    <input type="radio" id="statusEnabled" name="status" value="1" checked>
                                    <label for="statusEnabled">启用</label>
                                </div>
                                <div class="radio-item">
                                    <input type="radio" id="statusDisabled" name="status" value="0">
                                    <label for="statusDisabled">禁用</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label required">指标描述</label>
                            <textarea class="form-textarea" id="description" placeholder="请输入指标描述" required></textarea>
                            <div class="help-text">详细描述该指标的作用和判定逻辑</div>
                        </div>
                    </div>
                </div>

                <!-- 判定规则 -->
                <div class="form-section">
                    <h2 class="section-title">判定规则</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">数据源</label>
                            <select class="form-select" id="dataSource" required>
                                <option value="">请选择数据源</option>
                                <option value="merchant_info">商户基础信息</option>
                                <option value="transaction_record">交易记录</option>
                                <option value="subsidy_record">补贴记录</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">计算字段</label>
                            <select class="form-select" id="calcField" required>
                                <option value="">请选择字段</option>
                                <option value="registration_count">进件次数</option>
                                <option value="transaction_amount">交易金额</option>
                                <option value="subsidy_amount">补贴金额</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">计算方式</label>
                            <select class="form-select" id="calcMethod" required>
                                <option value="">请选择方式</option>
                                <option value="COUNT">计数</option>
                                <option value="SUM">求和</option>
                                <option value="AVG">平均值</option>
                                <option value="MAX">最大值</option>
                                <option value="MIN">最小值</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">统计周期</label>
                            <select class="form-select" id="calcPeriod" required>
                                <option value="">请选择周期</option>
                                <option value="ALL">全部时间</option>
                                <option value="MONTH">近一个月</option>
                                <option value="WEEK">近一周</option>
                                <option value="DAY">近一天</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 风险等级阈值 -->
                <div class="form-section">
                    <h2 class="section-title">风险等级阈值</h2>
                    
                    <div class="threshold-config">
                        <div class="threshold-item">
                            <span class="threshold-label high-risk">高风险:</span>
                            <span>>=</span>
                            <input type="number" class="form-input threshold-input" id="highRiskThreshold" placeholder="5" required>
                            <span>次</span>
                        </div>
                        <div class="threshold-item">
                            <span class="threshold-label medium-risk">中风险:</span>
                            <input type="number" class="form-input threshold-input" id="mediumRiskMin" placeholder="3" required>
                            <span>-</span>
                            <input type="number" class="form-input threshold-input" id="mediumRiskMax" placeholder="4" required>
                            <span>次</span>
                        </div>
                        <div class="threshold-item">
                            <span class="threshold-label low-risk">低风险:</span>
                            <span><</span>
                            <input type="number" class="form-input threshold-input" id="lowRiskThreshold" placeholder="3" required>
                            <span>次</span>
                        </div>
                    </div>

                    <div class="form-row" style="margin-top: 16px;">
                        <div class="form-group full-width">
                            <label class="form-label">风险说明</label>
                            <textarea class="form-textarea" id="riskDescription" placeholder="请输入风险说明，如：频繁进件可能表明商户存在资质问题或恶意套现行为"></textarea>
                            <div class="help-text">描述触发该风险指标时的风险原因和建议处理方式</div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 表单底部 -->
        <div class="form-footer">
            <button type="button" class="btn btn-default" onclick="window.close()">取消</button>
            <button type="submit" class="btn btn-primary" onclick="saveIndicator()">
                <i class="fas fa-save"></i>
                保存
            </button>
        </div>
    </div>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否为编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            const indicatorId = urlParams.get('id');

            if (indicatorId) {
                document.getElementById('formTitle').textContent = '编辑风险指标';
                loadIndicatorData(indicatorId);
            }

            // 绑定表单验证
            setupFormValidation();
        });

        // 加载指标数据（编辑模式）
        function loadIndicatorData(id) {
            // 模拟从服务器加载数据
            const mockData = {
                1: {
                    indicatorName: '进件次数',
                    indicatorCode: 'RISK_001',
                    category: 'PROFILE',
                    status: 1,
                    description: '统计商户历史进件申请次数，用于识别频繁进件的风险商户',
                    dataSource: 'merchant_info',
                    calcField: 'registration_count',
                    calcMethod: 'COUNT',
                    calcPeriod: 'ALL',
                    highRiskThreshold: 5,
                    mediumRiskMin: 3,
                    mediumRiskMax: 4,
                    lowRiskThreshold: 3,
                    riskDescription: '频繁进件可能表明商户存在资质问题或恶意套现行为，需要重点关注'
                }
            };

            const data = mockData[id];
            if (data) {
                document.getElementById('indicatorName').value = data.indicatorName;
                document.getElementById('indicatorCode').value = data.indicatorCode;
                document.getElementById('category').value = data.category;
                document.querySelector(`input[name="status"][value="${data.status}"]`).checked = true;
                document.getElementById('description').value = data.description;
                document.getElementById('dataSource').value = data.dataSource;
                document.getElementById('calcField').value = data.calcField;
                document.getElementById('calcMethod').value = data.calcMethod;
                document.getElementById('calcPeriod').value = data.calcPeriod;
                document.getElementById('highRiskThreshold').value = data.highRiskThreshold;
                document.getElementById('mediumRiskMin').value = data.mediumRiskMin;
                document.getElementById('mediumRiskMax').value = data.mediumRiskMax;
                document.getElementById('lowRiskThreshold').value = data.lowRiskThreshold;
                document.getElementById('riskDescription').value = data.riskDescription;
            }
        }

        // 设置表单验证
        function setupFormValidation() {
            // 指标编码格式验证
            const indicatorCode = document.getElementById('indicatorCode');
            indicatorCode.addEventListener('input', function() {
                const value = this.value;
                const pattern = /^[A-Z0-9_]+$/;

                if (value && !pattern.test(value)) {
                    showFieldError(this, '指标编码只能包含大写字母、数字和下划线');
                } else {
                    clearFieldError(this);
                }
            });

            // 阈值逻辑验证
            const thresholdInputs = ['highRiskThreshold', 'mediumRiskMin', 'mediumRiskMax', 'lowRiskThreshold'];
            thresholdInputs.forEach(id => {
                document.getElementById(id).addEventListener('blur', validateThresholds);
            });
        }

        // 验证阈值逻辑
        function validateThresholds() {
            const highRisk = parseFloat(document.getElementById('highRiskThreshold').value) || 0;
            const mediumMin = parseFloat(document.getElementById('mediumRiskMin').value) || 0;
            const mediumMax = parseFloat(document.getElementById('mediumRiskMax').value) || 0;
            const lowRisk = parseFloat(document.getElementById('lowRiskThreshold').value) || 0;

            let hasError = false;

            // 清除之前的错误
            clearFieldError(document.getElementById('highRiskThreshold'));
            clearFieldError(document.getElementById('mediumRiskMin'));
            clearFieldError(document.getElementById('mediumRiskMax'));
            clearFieldError(document.getElementById('lowRiskThreshold'));

            // 验证中风险范围
            if (mediumMin > mediumMax) {
                showFieldError(document.getElementById('mediumRiskMin'), '中风险最小值不能大于最大值');
                hasError = true;
            }

            // 验证阈值逻辑关系
            if (highRisk <= mediumMax) {
                showFieldError(document.getElementById('highRiskThreshold'), '高风险阈值应大于中风险最大值');
                hasError = true;
            }

            if (lowRisk >= mediumMin) {
                showFieldError(document.getElementById('lowRiskThreshold'), '低风险阈值应小于中风险最小值');
                hasError = true;
            }

            return !hasError;
        }

        // 显示字段错误
        function showFieldError(field, message) {
            clearFieldError(field);

            field.style.borderColor = 'var(--error-red)';

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-text';
            errorDiv.textContent = message;
            errorDiv.setAttribute('data-error-for', field.id);

            field.parentNode.appendChild(errorDiv);
        }

        // 清除字段错误
        function clearFieldError(field) {
            field.style.borderColor = '';

            const existingError = field.parentNode.querySelector(`[data-error-for="${field.id}"]`);
            if (existingError) {
                existingError.remove();
            }
        }

        // 保存指标
        function saveIndicator() {
            // 验证必填字段
            const requiredFields = [
                'indicatorName', 'indicatorCode', 'category', 'description',
                'dataSource', 'calcField', 'calcMethod', 'calcPeriod',
                'highRiskThreshold', 'mediumRiskMin', 'mediumRiskMax', 'lowRiskThreshold'
            ];

            let hasError = false;

            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (!field.value.trim()) {
                    showFieldError(field, '此字段为必填项');
                    hasError = true;
                } else {
                    clearFieldError(field);
                }
            });

            // 验证阈值逻辑
            if (!validateThresholds()) {
                hasError = true;
            }

            if (hasError) {
                alert('请检查并修正表单中的错误');
                return;
            }

            // 收集表单数据
            const formData = {
                indicatorName: document.getElementById('indicatorName').value,
                indicatorCode: document.getElementById('indicatorCode').value,
                category: document.getElementById('category').value,
                status: document.querySelector('input[name="status"]:checked').value,
                description: document.getElementById('description').value,
                dataSource: document.getElementById('dataSource').value,
                calcField: document.getElementById('calcField').value,
                calcMethod: document.getElementById('calcMethod').value,
                calcPeriod: document.getElementById('calcPeriod').value,
                highRiskThreshold: document.getElementById('highRiskThreshold').value,
                mediumRiskMin: document.getElementById('mediumRiskMin').value,
                mediumRiskMax: document.getElementById('mediumRiskMax').value,
                lowRiskThreshold: document.getElementById('lowRiskThreshold').value,
                riskDescription: document.getElementById('riskDescription').value
            };

            // 模拟保存操作
            console.log('保存指标数据:', formData);

            // 显示保存成功消息
            alert('保存成功！');

            // 关闭窗口并刷新父页面
            if (window.opener) {
                window.opener.loadIndicators();
            }
            window.close();
        }

        // 阻止表单默认提交
        document.getElementById('indicatorForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveIndicator();
        });
    </script>
</body>
</html>
