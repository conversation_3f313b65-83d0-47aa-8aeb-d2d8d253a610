1. 需求背景
当前已输出对商户资料、交易、补贴耗能等多维度的风险评估。但不足之处在于，所有风险指标的规则、阈值等均在代码层面实现，每次业务策略的调整都需要研发人员介入，响应周期长、灵活性差，且无法满足各省级机构精细化、差异化的风险管控需求。
2. 产品目标
- 配置化管理：将风险指标从代码中解耦，实现业务层面的可视化、可配置化管理。
- 提升灵活性：赋予省级机构自主管理和配置风险策略的能力，快速响应市场变化。
- 精细化风控：支持省级机构根据本地业务特点，定制个性化的风险阈值,实现差异化管理。
- 流程闭环：将风险识别与业务流程（如进件审核）打通，实现“识别-拦截-审批-放行”的自动化风控闭环。
2.1用户角色说明
暂时无法在飞书文档外展示此内容
3. 整体业务流程图
暂时无法在飞书文档外展示此内容
4. 功能逻辑
4.1 模块一：风险指标管理 
4.1.1.功能描述
由平台统一维护所有的风险指标，定义了“有什么”风险指标以及它们的“标准”判断规则。
4.1.2. 功能界面与逻辑
1. 指标库列表页
- UI设计建议: 采用标准的卡片+表格布局。页面顶部为筛选和搜索区，下方为指标列表。
- 界面元素:
  - 搜索框: 支持按“指标ID”、“指标名称”模糊搜索。
  - 筛选器:
    - 风险大类：下拉选择（资料风险、交易风险、补贴耗能风险等）。
    - 适用商户类型：下拉选择（全部、小微商户、企业商户）。
    - 状态：下拉选择（全部、已启用、已禁用）。
  - 操作按钮: “+ 新建指标”按钮。
  - 指标列表 (Table):
    - 指标ID: 系统唯一标识，如 RISK_001。
    - 指标名称: 指标的业务名称，如“短时多频进件”。
    - 分类: 所属分类。
    - 适用商户: 如“小微商户, 企业商户”支持展示多个，标签展示。
    - 状态: “已启用” / “已禁用” (使用开关控件 Switch)。
    - 创建时间：指标创建时间
    - 更新时间: 最近一次修改的时间。
    - 操作: 详情。
2. 新建/编辑指标页
- UI设计建议: 使用表单（Form）布局，分为几个区块：基本信息、规则定义。对于规则定义，采用可视化的规则构建器，降低使用门槛。
- 表单字段:
  - 指标名称: (必填) 文本输入框。
  - 分类: (必填) 下拉选择。
  - 适用商户: (必填) 复选框（小微商户、企业商户）。
  - 指标描述: (必填) 文本域。
  - 判定标准 (规则构建器): 核心功能，定义计算周期、统计维度和各风险等级的默认阈值。
4.2. 模块二：省级个性化配置 (省级功能) 
4.2.1. 功能描述
省级机构可在具体的风险评估页面（如资料风险、交易风险、补贴风险）直接进行指标配置，选择适用于本省的风险指标并启用，同时可对已启用的指标自定义风险等级的触发条件。
4.2.2. 功能界面与逻辑
3. 配置入口
- 在现有的 “资料风险评估”、“交易风险评估”、“补贴风险评估” 等页面的列表上方，增加一个 “指标配置” 按钮。
- 权限控制: 仅省级机构可见该按钮。
4. 省级风险指标配置弹窗
- 点击“指标配置”按钮后，弹出一个覆盖范围较大的模态框（Modal）。
- UI设计建议: 弹窗内使用表格布局，清晰地展示所有可配置的指标项。
- 界面元素:
  - 指标列表 (Table):
    - 指标名称: 显示总部定义的指标名称。
    - 总部基础规则: 只读展示总部的标准规则，作为参考。
    - 本省启用状态: 核心操作项。使用开关控件 Switch。
      - 开启: 代表该指标在本省生效，将用于评估省内商户。
      - 关闭: 代表该指标在本省不生效。
    - 本省规则:
      - 若指标未启用，则显示“-”。
      - 若指标已启用但未自定义，则显示“同总部规则”。
      - 若指标已启用且已自定义，则显示自定义后的规则，并用颜色或标签高亮，如“7日内进件次数 > 5次 [已自定义]”。
    - 操作:
      - 配置规则: 当“本省启用状态”为开启时，此按钮可用。点击后，弹出或展开规则配置界面。
      - 操作日志: 查看该指标在本省的配置修改历史。
5. 配置规则子弹窗/展开区域
- 点击配置规则后，在当前行下方展开或弹出新的配置窗口。
- UI设计建议: 自动填充默认的风险等级，可进行修改调整。
- 界面元素:
  - 同指标库的编辑页面，可进行风险等级阈值设定。
4.2.3. 核心逻辑
1. 数据加载: 打开“指标配置”弹窗时，系统根据当前页面（如“交易风险”）从总部指标库中筛选出对应风险大类的所有指标，并附加上当前省份的已有配置。
2. 风险计算:
  - 当对某商户进行风险计算时，系统首先获取商户所属省份。
  - 然后获取该省份已启用的风险指标列表。
  - 遍历列表，对每一个启用的指标：
    - 检查其是否使用了“自定义规则”。若是，则采用省内定义的阈值进行计算。
    - 若否，则采用默认的基础规则进行计算。
  - 省内未启用的指标，则在此次计算中完全跳过。
4.3. 模块三：与审核中心联动（说明）
2.4.1. 功能描述
在关键业务节点，实时调用风险计算引擎。当识别出高风险时，自动拦截当前操作，并为用户提供申请特批的入口，打通现有审核中心流程。
2.4.2. 交互流程设计
场景：一线业务员为某商户办理进件
1. 提交操作: 业务员在进件页面填写完所有资料，点击“提交”按钮。
2. 触发风控: 前端调用后端进件接口，后端业务逻辑首先调用风险计算引擎。
3. 返回结果:
  - 情况A：无高风险。接口返回成功，前端提示“进件成功”。流程结束。
  - 情况B：触发高风险。接口返回特定错误码（如 RISK_INTERCEPT）及风险详情。
4. 前端拦截与提示:
  - 前端捕获到 RISK_INTERCEPT 错误码后，弹出一个专门设计的提示框。
  - 提示框内容:
    - 标题: 操作已暂停
    - 正文: “系统检测到该商户触发以下高风险项，进件流程已暂停：”
    - 风险列表: 清晰列出触发的风险项和详情。
    - 按钮: 返回修改 (主按钮)、 申请特批 (次按钮)。
5. 申请特批:
  - 点击“申请特批”，弹出表单，自动带入商户信息和风险项。
  - 业务员填写申请理由后提交至审核中心。
6. 后续处理:
  - 审核通过后，取消拦截。
  - 业务员可再次重试提交，风控引擎将跳过检查，完成操作。
7. 非功能性需求
- 性能要求: 风险实时计算接口的响应时间（P95）应低于500ms，避免影响用户正常操作体验。
- 安全性要求:
  - 严格的权限控制，只有特定角色的用户才能访问对应的管理功能。
  - 所有在指标库和省级配置中的修改操作，都必须记录详细的操作日志。
- 扩展性要求: 风险大类、统计维度等应支持后续扩展，方便新类型风险的快速接入。
8. UI/UX 总体建议
9. 设计语言统一: 整体风格、色彩、字体、图标等应与“微邮付”现有系统保持一致。
10. 交互引导: 在关键配置项旁边，提供“问号”图标，鼠标悬浮时显示该配置项的详细解释和示例，降低用户的学习成本。
11. 反馈清晰: 无论是保存成功、操作失败还是风险拦截，系统都应给予用户清晰、明确、友好的反馈提示。