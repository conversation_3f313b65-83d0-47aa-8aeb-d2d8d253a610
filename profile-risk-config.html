<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资料风险指标配置</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-container);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 24px;
        }

        .config-header {
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color-light);
        }

        .config-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-description {
            color: var(--text-secondary);
            margin: 0;
        }

        .config-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-container);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .config-table th,
        .config-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-light);
        }

        .config-table th {
            background: var(--bg-layout);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }

        .config-table td {
            color: var(--text-primary);
            font-size: 14px;
        }

        .config-table tbody tr:hover {
            background: var(--bg-layout);
        }

        .merchant-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .merchant-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            border: 1px solid #91caff;
        }

        .status-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .status-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .switch-slider {
            background-color: var(--success-green);
        }

        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }

        .btn-text {
            background: none;
            border: none;
            color: var(--primary-blue);
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
        }

        .btn-text:hover {
            background: var(--primary-blue-light);
        }

        .btn-text:disabled {
            color: var(--text-disabled);
            cursor: not-allowed;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 15% auto;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            box-shadow: var(--shadow-card);
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: var(--bg-layout);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .btn-default:hover {
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }
    </style>
</head>
<body>
    <!-- 配置头部 -->
    <div class="config-header">
        <h1 class="config-title">
            <i class="fas fa-user-check" style="color: var(--primary-blue);"></i>
            资料风险指标配置
        </h1>
        <p class="config-description">配置资料风险相关指标的阈值和启用状态</p>
    </div>

    <!-- 配置表格 -->
    <table class="config-table">
        <thead>
            <tr>
                <th>指标名称</th>
                <th>适用商户</th>
                <th>状态</th>
                <th style="width: 120px;">操作</th>
            </tr>
        </thead>
        <tbody id="configTableBody">
            <!-- 数据将通过JavaScript动态加载 -->
        </tbody>
    </table>

    <!-- 配置规则弹窗 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="configModalTitle">配置风险指标</h3>
                <button class="modal-close" onclick="closeConfigModal()">&times;</button>
            </div>
            <div class="modal-body" id="configModalBody">
                <!-- 配置表单内容将动态加载 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeConfigModal()">取消</button>
                <button class="btn btn-primary" onclick="saveConfig()">确定</button>
            </div>
        </div>
    </div>
    <script>
        // 模拟数据
        const mockData = [
            {
                id: 1,
                indicatorName: '进件次数',
                applicableMerchants: ['个体工商户', '小微企业'],
                enabled: true,
                highRiskThreshold: '>=3次',
                mediumRiskThreshold: '2次',
                lowRiskThreshold: '<2次'
            },
            {
                id: 2,
                indicatorName: '门头照重复',
                applicableMerchants: ['个体工商户', '小微企业', '企业商户'],
                enabled: true,
                highRiskThreshold: '>=3次',
                mediumRiskThreshold: '2次',
                lowRiskThreshold: '<2次'
            },
            {
                id: 3,
                indicatorName: '三照导入方式',
                applicableMerchants: ['个体工商户'],
                enabled: false,
                highRiskThreshold: '手动导入',
                mediumRiskThreshold: '混合导入',
                lowRiskThreshold: '自动导入'
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfigData();
        });

        // 加载配置数据
        function loadConfigData() {
            const tbody = document.getElementById('configTableBody');
            tbody.innerHTML = '';

            mockData.forEach(item => {
                // 生成适用商户标签
                const merchantTags = item.applicableMerchants.map(merchant =>
                    `<span class="merchant-tag">${merchant}</span>`
                ).join('');

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 500;">${item.indicatorName}</div>
                    </td>
                    <td>
                        <div class="merchant-tags">${merchantTags}</div>
                    </td>
                    <td>
                        <label class="status-switch">
                            <input type="checkbox" ${item.enabled ? 'checked' : ''} onchange="toggleIndicator(${item.id}, this.checked)">
                            <span class="switch-slider"></span>
                        </label>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-text" ${item.enabled ? '' : 'disabled'} onclick="configIndicator(${item.id})">配置规则</button>
                            <button class="btn-text" onclick="viewHistory(${item.id})">操作日志</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 切换指标启用状态
        function toggleIndicator(id, enabled) {
            const item = mockData.find(item => item.id === id);
            if (item) {
                item.enabled = enabled;
                loadConfigData(); // 重新加载数据以更新按钮状态
            }
            console.log(`指标 ${id} ${enabled ? '启用' : '禁用'}`);
        }

        // 配置指标规则
        function configIndicator(id) {
            const indicator = mockData.find(item => item.id === id);
            if (!indicator) return;

            // 设置弹窗标题
            document.getElementById('configModalTitle').textContent = `配置风险指标 - ${indicator.indicatorName}`;

            // 生成配置表单
            const modalBody = document.getElementById('configModalBody');
            modalBody.innerHTML = `
                <div style="margin-bottom: 24px;">
                    <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">风险等级阈值设定</h4>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--error-red);">高风险阈值:</label>
                        <input type="text" value="${indicator.highRiskThreshold}" style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" placeholder="如：>=5次">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--warning-orange);">中风险阈值:</label>
                        <input type="text" value="${indicator.mediumRiskThreshold}" style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" placeholder="如：3-4次">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--success-green);">低风险阈值:</label>
                        <input type="text" value="${indicator.lowRiskThreshold}" style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" placeholder="如：<3次">
                    </div>
                </div>
            `;

            // 显示弹窗
            document.getElementById('configModal').style.display = 'block';
        }

        // 关闭配置弹窗
        function closeConfigModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // 保存配置
        function saveConfig() {
            console.log('保存配置');
            alert('配置保存成功！');
            closeConfigModal();
            loadConfigData(); // 重新加载数据
        }

        // 查看操作历史
        function viewHistory(id) {
            console.log('查看指标操作历史:', id);
            alert('操作历史功能开发中...');
        }

        // 点击弹窗背景关闭弹窗
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('configModal');
            if (e.target === modal) {
                closeConfigModal();
            }
        });
    </script>
</body>
</html>
