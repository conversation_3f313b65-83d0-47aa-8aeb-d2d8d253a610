<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>省级风险指标配置 - 微邮付商户健康度监测系统</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 24px;
        }

        .page-header {
            background: var(--bg-container);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-info h1 {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .header-info p {
            color: var(--text-secondary);
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .btn-default:hover {
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .config-section {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .section-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--bg-layout);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-stats {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .expand-btn {
            background: none;
            border: none;
            color: var(--primary-blue);
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .section-content {
            display: none;
        }

        .section-content.expanded {
            display: block;
        }

        .config-table {
            width: 100%;
            border-collapse: collapse;
        }

        .config-table th,
        .config-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-light);
        }

        .config-table th {
            background: var(--bg-layout);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }

        .config-table td {
            color: var(--text-primary);
            font-size: 14px;
        }

        .config-table tbody tr:hover {
            background: var(--bg-layout);
        }

        .status-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 22px;
        }

        .status-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 22px;
        }

        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .switch-slider {
            background-color: var(--primary-blue);
        }

        input:checked + .switch-slider:before {
            transform: translateX(22px);
        }

        .config-status {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .config-status.enabled {
            background: #f6ffed;
            color: var(--success-green);
            border: 1px solid #b7eb8f;
        }

        .config-status.disabled {
            background: #fff2f0;
            color: var(--error-red);
            border: 1px solid #ffccc7;
        }

        .config-status.default {
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            border: 1px solid #91caff;
        }

        .config-status.custom {
            background: #fff7e6;
            color: var(--warning-orange);
            border: 1px solid #ffd591;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-text {
            background: none;
            border: none;
            color: var(--primary-blue);
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
        }

        .btn-text:hover {
            background: var(--primary-blue-light);
        }

        .btn-text:disabled {
            color: var(--text-disabled);
            cursor: not-allowed;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: var(--bg-container);
            margin: 5% auto;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            box-shadow: var(--shadow-card);
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-primary);
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid var(--border-color-light);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: var(--bg-layout);
        }

        .merchant-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .merchant-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            border: 1px solid #91caff;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-info">
            <h1>风险配置管理 - <span id="provinceName">广东省</span></h1>
            <p>当前配置状态: 共<span id="totalIndicators">15</span>个指标，已配置<span id="configuredIndicators">12</span>个，未配置<span id="unconfiguredIndicators">3</span>个</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-default" onclick="exportConfig()">
                <i class="fas fa-download"></i>
                导出配置
            </button>
            <button class="btn btn-default" onclick="importConfig()">
                <i class="fas fa-upload"></i>
                批量导入
            </button>
            <button class="btn btn-primary" onclick="viewConfigHistory()">
                <i class="fas fa-history"></i>
                配置历史
            </button>
        </div>
    </div>

    <!-- 资料风险指标配置 -->
    <div class="config-section">
        <div class="section-header">
            <div>
                <h2 class="section-title">
                    <i class="fas fa-user-check" style="color: var(--primary-blue);"></i>
                    资料风险指标
                    <span class="section-stats">(5/6)</span>
                </h2>
            </div>
            <button class="expand-btn" onclick="toggleSection('profile')">
                <span>展开</span>
                <i class="fas fa-chevron-down" id="profile-icon"></i>
            </button>
        </div>
        <div class="section-content" id="profile-content">
            <table class="config-table">
                <thead>
                    <tr>
                        <th>指标名称</th>
                        <th>适用商户</th>
                        <th>状态</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="profileTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 交易风险指标配置 -->
    <div class="config-section">
        <div class="section-header">
            <div>
                <h2 class="section-title">
                    <i class="fas fa-chart-line" style="color: var(--warning-orange);"></i>
                    交易风险指标
                    <span class="section-stats">(4/5)</span>
                </h2>
            </div>
            <button class="expand-btn" onclick="toggleSection('transaction')">
                <span>展开</span>
                <i class="fas fa-chevron-down" id="transaction-icon"></i>
            </button>
        </div>
        <div class="section-content" id="transaction-content">
            <table class="config-table">
                <thead>
                    <tr>
                        <th>指标名称</th>
                        <th>适用商户</th>
                        <th>状态</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="transactionTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 补贴风险指标配置 -->
    <div class="config-section">
        <div class="section-header">
            <div>
                <h2 class="section-title">
                    <i class="fas fa-coins" style="color: var(--success-green);"></i>
                    补贴风险指标
                    <span class="section-stats">(3/4)</span>
                </h2>
            </div>
            <button class="expand-btn" onclick="toggleSection('subsidy')">
                <span>展开</span>
                <i class="fas fa-chevron-down" id="subsidy-icon"></i>
            </button>
        </div>
        <div class="section-content" id="subsidy-content">
            <table class="config-table">
                <thead>
                    <tr>
                        <th>指标名称</th>
                        <th>适用商户</th>
                        <th>状态</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="subsidyTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
    <!-- 指标配置弹窗 -->
    <div id="configModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="configModalTitle">配置风险指标</h3>
                <button class="modal-close" onclick="closeConfigModal()">&times;</button>
            </div>
            <div class="modal-body" id="configModalBody">
                <!-- 配置表单内容将动态加载 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeConfigModal()">取消</button>
                <button class="btn btn-primary" onclick="saveConfig()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = {
            profile: [
                {
                    id: 1,
                    indicatorName: '进件次数',
                    applicableMerchants: ['个体工商户', '小微企业'],
                    enabled: true,
                    highRiskThreshold: '>=3次',
                    mediumRiskThreshold: '2次',
                    lowRiskThreshold: '<2次'
                },
                {
                    id: 2,
                    indicatorName: '门头照重复',
                    applicableMerchants: ['个体工商户', '小微企业', '企业商户'],
                    enabled: true,
                    highRiskThreshold: '>=3次',
                    mediumRiskThreshold: '2次',
                    lowRiskThreshold: '<2次'
                },
                {
                    id: 3,
                    indicatorName: '三照导入方式',
                    applicableMerchants: ['个体工商户'],
                    enabled: false,
                    highRiskThreshold: '手动导入',
                    mediumRiskThreshold: '混合导入',
                    lowRiskThreshold: '自动导入'
                }
            ],
            transaction: [
                {
                    id: 4,
                    indicatorName: '大额交易',
                    applicableMerchants: ['企业商户', '连锁商户'],
                    enabled: true,
                    highRiskThreshold: '>=8万',
                    mediumRiskThreshold: '5-8万',
                    lowRiskThreshold: '<5万'
                },
                {
                    id: 5,
                    indicatorName: 'IP异常交易',
                    applicableMerchants: ['企业商户', '连锁商户'],
                    enabled: true,
                    highRiskThreshold: '>=5次',
                    mediumRiskThreshold: '3-4次',
                    lowRiskThreshold: '<3次'
                }
            ],
            subsidy: [
                {
                    id: 6,
                    indicatorName: '补贴激增',
                    applicableMerchants: ['个体工商户'],
                    enabled: true,
                    highRiskThreshold: '>200%',
                    mediumRiskThreshold: '100-200%',
                    lowRiskThreshold: '<100%'
                }
            ]
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 根据URL参数确定显示的分类
            const urlParams = new URLSearchParams(window.location.search);
            const category = urlParams.get('category') || 'PROFILE';

            loadAllSections();

            // 如果指定了分类，自动展开对应的区域
            if (category === 'PROFILE') {
                toggleSection('profile');
            } else if (category === 'TRANSACTION') {
                toggleSection('transaction');
            } else if (category === 'SUBSIDY') {
                toggleSection('subsidy');
            }
        });

        // 加载所有区域的数据
        function loadAllSections() {
            loadSectionData('profile', mockData.profile);
            loadSectionData('transaction', mockData.transaction);
            loadSectionData('subsidy', mockData.subsidy);
        }

        // 加载区域数据
        function loadSectionData(section, data) {
            const tbody = document.getElementById(`${section}TableBody`);
            tbody.innerHTML = '';

            data.forEach(item => {
                // 生成适用商户标签
                const merchantTags = item.applicableMerchants.map(merchant =>
                    `<span class="merchant-tag">${merchant}</span>`
                ).join('');

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: 500;">${item.indicatorName}</div>
                    </td>
                    <td>
                        <div class="merchant-tags">${merchantTags}</div>
                    </td>
                    <td>
                        <label class="status-switch">
                            <input type="checkbox" ${item.enabled ? 'checked' : ''} onchange="toggleIndicator(${item.id}, this.checked)">
                            <span class="switch-slider"></span>
                        </label>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-text" ${item.enabled ? '' : 'disabled'} onclick="configIndicator(${item.id})">配置规则</button>
                            <button class="btn-text" onclick="viewHistory(${item.id})">操作日志</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 切换区域展开/收起
        function toggleSection(section) {
            const content = document.getElementById(`${section}-content`);
            const icon = document.getElementById(`${section}-icon`);

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                content.classList.add('expanded');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }

        // 切换指标启用状态
        function toggleIndicator(id, enabled) {
            console.log(`指标 ${id} ${enabled ? '启用' : '禁用'}`);

            // 更新按钮状态
            const row = event.target.closest('tr');
            const configBtn = row.querySelector('.btn-text');
            configBtn.disabled = !enabled;

            // 模拟更新数据
            updateIndicatorStatus(id, enabled);

            // 重新加载数据以更新显示
            loadAllSections();
        }

        // 更新指标状态
        function updateIndicatorStatus(id, enabled) {
            // 在实际应用中，这里会调用API更新数据
            for (let category in mockData) {
                const item = mockData[category].find(item => item.id === id);
                if (item) {
                    item.enabled = enabled;
                    if (!enabled) {
                        item.customized = false;
                        item.riskLevel = '-';
                        item.threshold = '未配置';
                    }
                    break;
                }
            }
        }

        // 配置指标规则
        function configIndicator(id) {
            // 查找指标数据
            let indicator = null;
            for (let category in mockData) {
                indicator = mockData[category].find(item => item.id === id);
                if (indicator) break;
            }

            if (!indicator) return;

            // 设置弹窗标题
            document.getElementById('configModalTitle').textContent = `配置风险指标 - ${indicator.indicatorName}`;

            // 生成配置表单
            const modalBody = document.getElementById('configModalBody');
            modalBody.innerHTML = `
                <div style="margin-bottom: 24px;">
                    <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600;">风险等级阈值设定</h4>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--error-red);">高风险阈值:</label>
                        <input type="text" value="${indicator.highRiskThreshold}" style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" placeholder="如：>=5次">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--warning-orange);">中风险阈值:</label>
                        <input type="text" value="${indicator.mediumRiskThreshold}" style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" placeholder="如：3-4次">
                    </div>

                    <div style="margin-bottom: 16px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--success-green);">低风险阈值:</label>
                        <input type="text" value="${indicator.lowRiskThreshold}" style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 14px;" placeholder="如：<3次">
                    </div>
                </div>
            `;

            // 显示弹窗
            document.getElementById('configModal').style.display = 'block';
        }

        // 关闭配置弹窗
        function closeConfigModal() {
            document.getElementById('configModal').style.display = 'none';
        }

        // 保存配置
        function saveConfig() {
            console.log('保存配置');
            alert('配置保存成功！');
            closeConfigModal();
            loadAllSections(); // 重新加载数据
        }

        // 查看操作历史
        function viewHistory(id) {
            console.log('查看指标操作历史:', id);
            alert('操作历史功能开发中...');
        }

        // 导出配置
        function exportConfig() {
            console.log('导出配置');
            alert('配置导出功能开发中...');
        }

        // 批量导入
        function importConfig() {
            console.log('批量导入');
            alert('批量导入功能开发中...');
        }

        // 查看配置历史
        function viewConfigHistory() {
            console.log('查看配置历史');
            alert('配置历史功能开发中...');
        }

        // 点击弹窗背景关闭弹窗
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('configModal');
            if (e.target === modal) {
                closeConfigModal();
            }
        });
    </script>
</body>
</html>
