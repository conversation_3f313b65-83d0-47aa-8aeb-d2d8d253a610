<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户补贴风险评估详情</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: end;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: var(--primary-blue-light);
        }

        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .info-value {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .risk-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .risk-high {
            background: #fff2f0;
            color: var(--error-red);
        }

        .risk-medium {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        .risk-low {
            background: #f6ffed;
            color: var(--success-green);
        }

        .alert {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            border: 1px solid;
        }

        .alert-warning {
            background: #fffbe6;
            border-color: #ffe58f;
            color: #d48806;
        }

        .alert-danger {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #cf1322;
        }

        .alert-success {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #389e0d;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .data-table th {
            background: #fafafa;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
        }

        .data-table td {
            padding: 12px 16px;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
            color: var(--text-primary);
        }

        .data-table tr:hover {
            background: #fafafa;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f5f5f5;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 8px;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .progress-high {
            background: var(--error-red);
        }

        .progress-medium {
            background: var(--warning-orange);
        }

        .progress-low {
            background: var(--success-green);
        }

        .metric-card {
            background: var(--bg-container);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .metric-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .metric-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
            border-radius: 8px;
            color: var(--text-secondary);
        }

        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .btn-default:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color-light);
            margin-bottom: 24px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: var(--text-secondary);
            transition: all 0.2s ease;
        }

        .tab.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
        }

        .tab:hover {
            color: var(--primary-blue);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <a href="#" class="back-btn" onclick="window.history.back()">
                <i class="fas fa-arrow-left"></i>
                返回列表
            </a>
        </div>

        <!-- 商户基本信息 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">商户基本信息</h3>
                <span class="risk-tag risk-high">高风险</span>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">商户号</div>
                        <div class="info-value">M202401001</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">商户名称</div>
                        <div class="info-value">张三便利店</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">联系电话</div>
                        <div class="info-value">138****5678</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">市级分公司</div>
                        <div class="info-value">北京分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">区县分公司</div>
                        <div class="info-value">朝阳区分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">营业所</div>
                        <div class="info-value">朝阳路营业所</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 风险评估概览 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">补贴风险评估概览</h3>
                <div class="flex gap-2">
                    <button class="btn btn-default">
                        <i class="fas fa-download"></i>
                        导出报告
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>高风险警告：</strong>该商户补贴申请存在异常模式，建议立即进行人工审核。
                </div>

                <div class="info-grid">
                    <div class="metric-card">
                        <div class="metric-number" style="color: var(--error-red);">85</div>
                        <div class="metric-label">风险评分</div>
                        <div class="progress-bar">
                            <div class="progress-fill progress-high" style="width: 85%;"></div>
                        </div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" style="color: var(--primary-blue);">¥12,580</div>
                        <div class="metric-label">本月补贴金额</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-number" style="color: var(--warning-orange);">¥45,230</div>
                        <div class="metric-label">累计补贴金额</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细评估信息 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">详细评估信息</h3>
            </div>
            <div class="card-body">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('subsidy-details')">补贴详情</div>
                    <div class="tab" onclick="switchTab('risk-analysis')">风险分析</div>
                    <div class="tab" onclick="switchTab('transaction-pattern')">交易模式</div>
                    <div class="tab" onclick="switchTab('history-record')">历史记录</div>
                </div>

                <!-- 补贴详情 -->
                <div id="subsidy-details" class="tab-content active">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">补贴类型</div>
                            <div class="info-value">交易补贴</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">补贴标准</div>
                            <div class="info-value">0.38% / 笔</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">评估月份</div>
                            <div class="info-value">2024年1月</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">交易笔数</div>
                            <div class="info-value">3,305 笔</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">交易金额</div>
                            <div class="info-value">¥3,310,526</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">补贴金额</div>
                            <div class="info-value">¥12,580</div>
                        </div>
                    </div>

                    <h4 style="margin: 24px 0 16px 0; font-size: 16px; font-weight: 500;">补贴明细</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>交易笔数</th>
                                <th>交易金额</th>
                                <th>补贴金额</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-01</td>
                                <td>125</td>
                                <td>¥125,680</td>
                                <td>¥477.58</td>
                                <td><span class="risk-tag risk-low">正常</span></td>
                            </tr>
                            <tr>
                                <td>2024-01-02</td>
                                <td>98</td>
                                <td>¥98,450</td>
                                <td>¥374.11</td>
                                <td><span class="risk-tag risk-low">正常</span></td>
                            </tr>
                            <tr>
                                <td>2024-01-15</td>
                                <td>456</td>
                                <td>¥456,890</td>
                                <td>¥1,736.18</td>
                                <td><span class="risk-tag risk-high">异常</span></td>
                            </tr>
                            <tr>
                                <td>2024-01-28</td>
                                <td>389</td>
                                <td>¥389,120</td>
                                <td>¥1,478.66</td>
                                <td><span class="risk-tag risk-medium">可疑</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 风险分析 -->
                <div id="risk-analysis" class="tab-content">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle"></i>
                        <strong>风险提示：</strong>检测到多项风险指标异常，建议重点关注。
                    </div>

                    <h4 style="margin: 24px 0 16px 0; font-size: 16px; font-weight: 500;">风险指标分析</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>风险指标</th>
                                <th>当前值</th>
                                <th>阈值</th>
                                <th>风险等级</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>单日交易笔数增长率</td>
                                <td>+285%</td>
                                <td>&lt;50%</td>
                                <td><span class="risk-tag risk-high">高风险</span></td>
                                <td>单日交易笔数异常增长</td>
                            </tr>
                            <tr>
                                <td>补贴金额占比</td>
                                <td>0.38%</td>
                                <td>&lt;0.30%</td>
                                <td><span class="risk-tag risk-medium">中风险</span></td>
                                <td>补贴占交易金额比例偏高</td>
                            </tr>
                            <tr>
                                <td>交易时间集中度</td>
                                <td>78%</td>
                                <td>&lt;60%</td>
                                <td><span class="risk-tag risk-high">高风险</span></td>
                                <td>交易时间过于集中</td>
                            </tr>
                            <tr>
                                <td>重复交易率</td>
                                <td>12%</td>
                                <td>&lt;5%</td>
                                <td><span class="risk-tag risk-medium">中风险</span></td>
                                <td>存在重复交易行为</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 交易模式 -->
                <div id="transaction-pattern" class="tab-content">
                    <div class="chart-container">
                        <div>
                            <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 16px;"></i>
                            <div>交易模式分析图表</div>
                            <div style="font-size: 12px; margin-top: 8px;">（此处可集成实际图表组件）</div>
                        </div>
                    </div>

                    <h4 style="margin: 24px 0 16px 0; font-size: 16px; font-weight: 500;">异常交易模式</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">高频交易时段</div>
                            <div class="info-value">14:00-16:00 (78%)</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">平均交易金额</div>
                            <div class="info-value">¥1,001.68</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">异常交易占比</div>
                            <div class="info-value">23.5%</div>
                        </div>
                    </div>
                </div>

                <!-- 历史记录 -->
                <div id="history-record" class="tab-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>评估月份</th>
                                <th>补贴金额</th>
                                <th>风险等级</th>
                                <th>处理状态</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01</td>
                                <td>¥12,580</td>
                                <td><span class="risk-tag risk-high">高风险</span></td>
                                <td>待处理</td>
                                <td>当前评估</td>
                            </tr>
                            <tr>
                                <td>2023-12</td>
                                <td>¥8,920</td>
                                <td><span class="risk-tag risk-medium">中风险</span></td>
                                <td>已处理</td>
                                <td>已整改</td>
                            </tr>
                            <tr>
                                <td>2023-11</td>
                                <td>¥6,450</td>
                                <td><span class="risk-tag risk-low">低风险</span></td>
                                <td>正常</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>2023-10</td>
                                <td>¥7,280</td>
                                <td><span class="risk-tag risk-low">低风险</span></td>
                                <td>正常</td>
                                <td>-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 处理建议 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">处理建议</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h4 style="margin: 0 0 12px 0; font-size: 16px;">紧急处理建议</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>立即暂停该商户的补贴发放</li>
                        <li>安排实地走访核实交易真实性</li>
                        <li>调取近3个月完整交易记录进行分析</li>
                        <li>联系商户进行情况说明</li>
                    </ul>
                </div>

                <div style="margin-top: 24px;">
                    <h4 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 500;">后续跟进措施</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">责任人</div>
                            <div class="info-value">张经理</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">跟进期限</div>
                            <div class="info-value">3个工作日</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">处理状态</div>
                            <div class="info-value">待处理</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabId) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabId).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            const merchantId = getUrlParameter('id');
            if (merchantId) {
                console.log('商户ID:', merchantId);
                // 这里可以根据商户ID加载具体数据
            }
        });
    </script>
</body>
</html>
