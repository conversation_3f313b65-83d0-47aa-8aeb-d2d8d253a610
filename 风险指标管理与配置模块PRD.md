# 风险指标管理与配置模块 PRD 文档

## 1. 项目概述

### 1.1 项目背景
邮政"微邮付"业务商户健康度评估系统已实现商户资料风险评估、交易风险评估、补贴耗能风险评估等功能。现有风险指标仅在技术层面存储和计算，缺乏业务层面的管理维护机制。为提升系统的灵活性和可配置性，需要新增风险指标管理与配置模块。

### 1.2 项目目标
- 建立统一的风险指标管理体系
- 支持省级机构个性化风险配置
- 实现风险指标与业务流程的联动
- 提升系统的可维护性和扩展性

### 1.3 项目范围
- 风险指标库的统一管理
- 省级机构个性化配置功能
- 与审核中心的联动机制
- 相关页面和交互组件的开发

## 2. 功能架构

### 2.1 整体架构图

```mermaid
graph TB
    A[风险指标管理与配置模块] --> B[风险指标库管理]
    A --> C[省级机构配置]
    A --> D[业务联动控制]
    
    B --> B1[指标定义管理]
    B --> B2[判定规则配置]
    B --> B3[指标分类管理]
    B --> B4[指标状态管理]
    
    C --> C1[指标选择配置]
    C --> C2[阈值自定义]
    C --> C3[风险等级设置]
    C --> C4[配置版本管理]
    
    D --> D1[业务操作拦截]
    D --> D2[审批流程触发]
    D --> D3[风险预警通知]
    D --> D4[数据统计分析]
    
    E[现有系统] --> E1[商户资料风险评估]
    E --> E2[交易风险评估]
    E --> E3[补贴耗能风险评估]
    E --> E4[审核中心]
    
    A -.-> E
```

### 2.2 模块关系说明

| 模块 | 功能描述 | 依赖关系 |
|------|----------|----------|
| 风险指标库管理 | 统一管理所有风险指标的定义、规则和状态 | 基础模块，被其他模块依赖 |
| 省级机构配置 | 各省级机构个性化配置风险指标和阈值 | 依赖风险指标库管理 |
| 业务联动控制 | 根据风险配置控制业务流程和触发审批 | 依赖省级机构配置 |

## 3. 业务流程

### 3.1 风险指标管理流程

```mermaid
flowchart TD
    A[系统管理员登录] --> B[进入风险指标管理]
    B --> C{选择操作类型}
    
    C -->|新增指标| D[填写指标基本信息]
    C -->|编辑指标| E[选择现有指标]
    C -->|删除指标| F[选择要删除的指标]
    C -->|查看指标| G[查看指标列表]
    
    D --> H[设置判定规则]
    E --> H
    H --> I[配置风险等级阈值]
    I --> J[设置指标分类]
    J --> K[保存指标配置]
    
    F --> L{检查指标使用情况}
    L -->|已被使用| M[提示无法删除]
    L -->|未被使用| N[确认删除]
    N --> O[删除成功]
    
    K --> P[配置生效]
    G --> Q[展示指标列表]
    
    M --> B
    O --> B
    P --> B
    Q --> B
```

### 3.2 省级机构配置流程

```mermaid
flowchart TD
    A[省级管理员登录] --> B[进入风险配置管理]
    B --> C[查看当前配置状态]
    C --> D{选择操作}
    
    D -->|新增配置| E[选择风险指标]
    D -->|修改配置| F[选择已配置指标]
    D -->|删除配置| G[选择要删除的配置]
    D -->|批量导入| H[上传配置文件]
    
    E --> I[设置风险等级]
    F --> I
    I --> J[配置判定阈值]
    J --> K[设置生效时间]
    K --> L[预览配置效果]
    L --> M{确认配置}
    
    M -->|确认| N[保存配置]
    M -->|取消| B
    
    G --> O{检查配置依赖}
    O -->|有依赖| P[提示无法删除]
    O -->|无依赖| Q[确认删除]
    Q --> R[删除成功]
    
    H --> S[验证文件格式]
    S --> T{验证结果}
    T -->|成功| U[批量导入配置]
    T -->|失败| V[显示错误信息]
    
    N --> W[配置生效通知]
    U --> W
    W --> X[更新商户风险评估]
    
    P --> B
    R --> B
    V --> B
    X --> B
```

### 3.3 业务联动控制流程

```mermaid
flowchart TD
    A[商户执行业务操作] --> B[获取商户所属省份]
    B --> C[查询省级风险配置]
    C --> D[计算商户风险指标]
    D --> E{是否触发高风险}
    
    E -->|否| F[允许操作继续]
    E -->|是| G[拦截业务操作]
    
    G --> H[显示风险提示]
    H --> I{用户选择}
    
    I -->|取消操作| J[操作终止]
    I -->|申请审批| K[跳转审核中心]
    
    K --> L[填写申请信息]
    L --> M[提交审批申请]
    M --> N[等待审批结果]
    
    N --> O{审批结果}
    O -->|通过| P[允许操作继续]
    O -->|拒绝| Q[操作被拒绝]
    O -->|待审批| R[继续等待]
    
    F --> S[记录操作日志]
    P --> S
    Q --> T[记录拒绝日志]
    J --> U[记录取消日志]
    
    S --> V[操作完成]
    T --> V
    U --> V
    R --> N
```

## 4. 页面设计

### 4.1 风险指标管理页面

#### 4.1.1 指标列表页面

**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│ 风险指标管理                                                │
├─────────────────────────────────────────────────────────────┤
│ [新增指标] [批量导入] [导出配置]                            │
│                                                             │
│ 搜索: [指标名称____] [分类▼] [状态▼] [搜索]                │
├─────────────────────────────────────────────────────────────┤
│ ┌─┬──────────┬────────┬────────┬────────┬────────┬────────┐ │
│ │✓│指标名称  │分类    │状态    │创建时间│更新时间│操作    │ │
│ ├─┼──────────┼────────┼────────┼────────┼────────┼────────┤ │
│ │ │进件次数  │资料风险│启用    │2024-01 │2024-01 │编辑|删除│ │
│ │ │大额交易  │交易风险│启用    │2024-01 │2024-01 │编辑|删除│ │
│ │ │补贴激增  │补贴风险│禁用    │2024-01 │2024-01 │编辑|删除│ │
│ └─┴──────────┴────────┴────────┴────────┴────────┴────────┘ │
│                                                             │
│ [批量启用] [批量禁用] [批量删除]        [1][2][3]...[10] │
└─────────────────────────────────────────────────────────────┘
```

**功能特性：**
- 支持多条件搜索和筛选
- 支持批量操作（启用/禁用/删除）
- 分页显示，每页20条记录
- 支持按列排序

#### 4.1.2 新增/编辑指标页面

**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│ 新增风险指标                                    [保存][取消] │
├─────────────────────────────────────────────────────────────┤
│ 基本信息                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 指标名称: [________________] *必填                      │ │
│ │ 指标编码: [________________] *必填，唯一                │ │
│ │ 指标分类: [资料风险▼] *必填                             │ │
│ │ 指标描述: [________________________________]            │ │
│ │          [________________________________]            │ │
│ │ 状态:     ○启用 ○禁用                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 判定规则                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 数据源:   [商户基础信息▼]                               │ │
│ │ 计算字段: [进件次数▼]                                   │ │
│ │ 计算方式: [累计值▼]                                     │ │
│ │ 统计周期: [全部时间▼]                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 风险等级阈值                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 高风险: >= [5] 次                                       │ │
│ │ 中风险: [3] - [4] 次                                    │ │
│ │ 低风险: < [3] 次                                        │ │
│ │                                                         │ │
│ │ 风险说明:                                               │ │
│ │ [频繁进件可能表明商户存在资质问题或恶意套现行为_______] │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 省级机构配置页面

#### 4.2.1 配置概览页面

**页面布局：**
```
┌─────────────────────────────────────────────────────────────┐
│ 风险配置管理 - 广东省                                       │
├─────────────────────────────────────────────────────────────┤
│ [新增配置] [批量导入] [导出配置] [配置历史]                 │
│                                                             │
│ 当前配置状态: 共15个指标，已配置12个，未配置3个             │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 资料风险指标 (5/6)                              [展开▼] │ │
│ │ ┌─┬──────────┬────────┬────────┬────────┬────────────┐   │ │
│ │ │✓│指标名称  │风险等级│阈值    │状态    │操作        │   │ │
│ │ ├─┼──────────┼────────┼────────┼────────┼────────────┤   │ │
│ │ │✓│进件次数  │高风险  │>=3次   │启用    │编辑|删除   │   │ │
│ │ │✓│门头照重复│中风险  │2-4次   │启用    │编辑|删除   │   │ │
│ │ │ │三照导入  │-       │未配置  │未配置  │配置        │   │ │
│ │ └─┴──────────┴────────┴────────┴────────┴────────────┘   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 交易风险指标 (4/5)                              [展开▼] │ │
│ │ ...                                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 补贴风险指标 (3/4)                              [展开▼] │ │
│ │ ...                                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 4.2.2 指标配置弹窗

**弹窗布局：**
```
┌─────────────────────────────────────────────────────────────┐
│ 配置风险指标 - 进件次数                         [×]         │
├─────────────────────────────────────────────────────────────┤
│ 指标信息                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 指标名称: 进件次数                                      │ │
│ │ 指标分类: 资料风险                                      │ │
│ │ 指标描述: 统计商户历史进件申请次数                      │ │
│ │ 默认阈值: 高风险>=5次，中风险3-4次，低风险<3次          │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 省级配置                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 启用状态: ○启用 ○禁用                                   │ │
│ │                                                         │ │
│ │ 风险等级配置:                                           │ │
│ │ 高风险: >= [3] 次 (默认5次)                             │ │
│ │ 中风险: [2] - [2] 次 (默认3-4次)                        │ │
│ │ 低风险: < [2] 次 (默认<3次)                             │ │
│ │                                                         │ │
│ │ 生效时间: [2024-01-01 00:00:00]                         │ │
│ │                                                         │ │
│ │ 配置说明: [广东省商户进件管控较严，降低风险阈值_______] │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 影响预估                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 根据当前配置，预计影响商户数量:                         │ │
│ │ • 新增高风险商户: 约156个                               │ │
│ │ • 新增中风险商户: 约89个                                │ │
│ │ • 可能触发业务限制的商户: 约156个                       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                                           [确定] [取消]     │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 业务联动提示页面

#### 4.3.1 风险拦截弹窗

**弹窗布局：**
```
┌─────────────────────────────────────────────────────────────┐
│ ⚠️ 业务操作风险提示                                [×]      │
├─────────────────────────────────────────────────────────────┤
│ 商户信息                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 商户名称: 张三便利店                                    │ │
│ │ 商户编号: 6026245559                                    │ │
│ │ 所属省份: 广东省                                        │ │
│ │ 当前操作: 商户进件申请                                  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 触发的风险指标                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🔴 进件次数 (高风险)                                    │ │
│ │    当前值: 7次 | 风险阈值: >=3次                        │ │
│ │    风险说明: 频繁进件可能存在资质问题                   │ │
│ │                                                         │ │
│ │ 🟡 门头照重复 (中风险)                                  │ │
│ │    当前值: 3次 | 风险阈值: 2-4次                        │ │
│ │    风险说明: 可能存在套牌经营行为                       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 处理建议                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 由于该商户触发高风险指标，建议进行人工审核后再决定是否  │ │
│ │ 允许继续操作。您可以选择:                               │ │
│ │                                                         │ │
│ │ 1. 取消当前操作                                         │ │
│ │ 2. 申请特殊审批，由上级审核后决定                       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│                                   [取消操作] [申请审批]     │
└─────────────────────────────────────────────────────────────┘
```

## 5. 交互逻辑

### 5.1 用户操作路径

#### 5.1.1 系统管理员操作路径
1. **登录系统** → **系统管理** → **风险指标管理**
2. **新增指标**: 点击"新增指标" → 填写基本信息 → 配置判定规则 → 设置阈值 → 保存
3. **编辑指标**: 选择指标 → 点击"编辑" → 修改信息 → 保存
4. **删除指标**: 选择指标 → 点击"删除" → 确认删除
5. **批量操作**: 勾选多个指标 → 选择批量操作 → 确认执行

#### 5.1.2 省级管理员操作路径
1. **登录系统** → **风险配置管理**
2. **新增配置**: 点击"新增配置" → 选择指标 → 设置阈值 → 预览效果 → 保存
3. **修改配置**: 选择已配置指标 → 点击"编辑" → 修改阈值 → 保存
4. **批量导入**: 点击"批量导入" → 上传文件 → 验证格式 → 确认导入

#### 5.1.3 业务用户操作路径
1. **执行业务操作** → **系统风险检查** → **触发风险拦截**
2. **查看风险提示** → **选择处理方式**:
   - 取消操作: 直接退出
   - 申请审批: 跳转审核中心 → 填写申请 → 提交审批

### 5.2 系统响应逻辑

#### 5.2.1 数据验证逻辑
- **指标名称**: 不能为空，长度2-50字符，同一分类下不能重复
- **指标编码**: 不能为空，只能包含字母、数字、下划线，全局唯一
- **阈值配置**: 数值类型验证，高风险阈值 > 中风险阈值 > 低风险阈值
- **生效时间**: 不能早于当前时间，格式验证

#### 5.2.2 业务规则验证
- **删除指标**: 检查是否被省级配置使用，如已使用则不允许删除
- **禁用指标**: 检查省级配置状态，如有省份正在使用则提示影响范围
- **配置冲突**: 检查同一指标是否存在时间重叠的配置

#### 5.2.3 权限控制逻辑
- **系统管理员**: 可管理所有风险指标，不能查看省级配置
- **省级管理员**: 只能配置本省风险指标，不能管理指标库
- **业务用户**: 只能查看风险提示，不能修改任何配置

## 6. 数据字段

### 6.1 风险指标库表 (risk_indicator)

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | 是 | 自增 | 主键ID |
| indicator_code | varchar | 50 | 是 | - | 指标编码，全局唯一 |
| indicator_name | varchar | 100 | 是 | - | 指标名称 |
| category | varchar | 20 | 是 | - | 指标分类：PROFILE/TRANSACTION/SUBSIDY |
| description | text | - | 否 | - | 指标描述 |
| data_source | varchar | 50 | 是 | - | 数据源表名 |
| calc_field | varchar | 50 | 是 | - | 计算字段 |
| calc_method | varchar | 20 | 是 | - | 计算方式：COUNT/SUM/AVG/MAX/MIN |
| calc_period | varchar | 20 | 是 | - | 统计周期：ALL/MONTH/WEEK/DAY |
| high_risk_threshold | decimal | 10,2 | 是 | - | 高风险阈值 |
| medium_risk_threshold | decimal | 10,2 | 是 | - | 中风险阈值 |
| low_risk_threshold | decimal | 10,2 | 是 | - | 低风险阈值 |
| risk_description | text | - | 否 | - | 风险说明 |
| status | tinyint | 1 | 是 | 1 | 状态：0-禁用，1-启用 |
| created_by | bigint | - | 是 | - | 创建人ID |
| created_time | datetime | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_by | bigint | - | 否 | - | 更新人ID |
| updated_time | datetime | - | 否 | - | 更新时间 |

### 6.2 省级风险配置表 (province_risk_config)

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | 是 | 自增 | 主键ID |
| province_code | varchar | 10 | 是 | - | 省份编码 |
| indicator_id | bigint | - | 是 | - | 风险指标ID |
| high_risk_threshold | decimal | 10,2 | 是 | - | 高风险阈值 |
| medium_risk_threshold | decimal | 10,2 | 是 | - | 中风险阈值 |
| low_risk_threshold | decimal | 10,2 | 是 | - | 低风险阈值 |
| effective_time | datetime | - | 是 | - | 生效时间 |
| expire_time | datetime | - | 否 | - | 失效时间 |
| config_description | text | - | 否 | - | 配置说明 |
| status | tinyint | 1 | 是 | 1 | 状态：0-禁用，1-启用 |
| created_by | bigint | - | 是 | - | 创建人ID |
| created_time | datetime | - | 是 | CURRENT_TIMESTAMP | 创建时间 |
| updated_by | bigint | - | 否 | - | 更新人ID |
| updated_time | datetime | - | 否 | - | 更新时间 |

### 6.3 风险评估结果表 (merchant_risk_assessment)

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | 是 | 自增 | 主键ID |
| merchant_id | varchar | 50 | 是 | - | 商户ID |
| province_code | varchar | 10 | 是 | - | 省份编码 |
| indicator_id | bigint | - | 是 | - | 风险指标ID |
| indicator_value | decimal | 10,2 | 是 | - | 指标值 |
| risk_level | varchar | 10 | 是 | - | 风险等级：HIGH/MEDIUM/LOW |
| assessment_time | datetime | - | 是 | CURRENT_TIMESTAMP | 评估时间 |
| config_version | varchar | 20 | 是 | - | 配置版本号 |

### 6.4 业务操作拦截记录表 (business_operation_block)

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint | - | 是 | 自增 | 主键ID |
| merchant_id | varchar | 50 | 是 | - | 商户ID |
| operation_type | varchar | 50 | 是 | - | 操作类型 |
| risk_indicators | text | - | 是 | - | 触发的风险指标JSON |
| block_time | datetime | - | 是 | CURRENT_TIMESTAMP | 拦截时间 |
| user_action | varchar | 20 | 是 | - | 用户操作：CANCEL/APPLY_APPROVAL |
| approval_id | bigint | - | 否 | - | 审批ID |
| approval_status | varchar | 20 | 否 | - | 审批状态 |
| created_by | bigint | - | 是 | - | 操作人ID |

## 7. 权限控制

### 7.1 角色定义

| 角色 | 角色代码 | 权限范围 | 主要职责 |
|------|----------|----------|----------|
| 系统管理员 | SYSTEM_ADMIN | 全系统 | 管理风险指标库，维护系统配置 |
| 省级管理员 | PROVINCE_ADMIN | 本省范围 | 配置本省风险指标阈值 |
| 业务用户 | BUSINESS_USER | 操作权限 | 执行业务操作，查看风险提示 |
| 审核人员 | AUDITOR | 审核权限 | 处理风险审批申请 |

### 7.2 功能权限矩阵

| 功能模块 | 系统管理员 | 省级管理员 | 业务用户 | 审核人员 |
|----------|------------|------------|----------|----------|
| 风险指标管理 | ✅ 增删改查 | ❌ | ❌ | ❌ |
| 省级配置管理 | ❌ | ✅ 本省配置 | ❌ | ❌ |
| 风险评估查看 | ✅ 全部 | ✅ 本省 | ✅ 相关商户 | ✅ 审批相关 |
| 业务操作拦截 | ❌ | ❌ | ✅ 被拦截 | ❌ |
| 审批处理 | ❌ | ❌ | ❌ | ✅ |

### 7.3 数据权限控制

#### 7.3.1 省级数据隔离
- 省级管理员只能查看和配置本省的风险配置
- 通过用户登录信息中的省份编码进行数据过滤
- 数据库查询时自动添加省份条件

#### 7.3.2 商户数据权限
- 业务用户只能查看有操作权限的商户风险信息
- 根据用户的业务权限范围进行数据过滤
- 敏感信息脱敏处理

#### 7.3.3 操作日志记录
- 所有配置变更操作记录操作人、操作时间、操作内容
- 敏感操作需要二次确认
- 重要配置变更需要审批流程

## 8. 异常处理

### 8.1 数据异常处理

#### 8.1.1 配置冲突处理
**场景**: 同一指标存在时间重叠的配置
**处理方案**:
1. 系统检测到配置时间冲突时，提示用户
2. 提供冲突解决选项：
   - 覆盖原配置
   - 调整生效时间
   - 取消当前配置
3. 记录冲突处理日志

#### 8.1.2 数据计算异常
**场景**: 风险指标计算过程中数据源异常
**处理方案**:
1. 设置数据源连接超时和重试机制
2. 计算失败时使用缓存数据或默认值
3. 记录异常日志，发送告警通知
4. 提供手动重新计算功能

#### 8.1.3 阈值配置异常
**场景**: 配置的阈值不符合逻辑关系
**处理方案**:
1. 前端实时验证阈值逻辑关系
2. 后端保存前再次验证
3. 异常时提示具体错误信息
4. 提供阈值建议值

### 8.2 业务异常处理

#### 8.2.1 风险评估服务异常
**场景**: 风险评估服务不可用
**处理方案**:
1. 设置服务降级策略，使用缓存结果
2. 异常时允许业务操作继续，记录风险日志
3. 服务恢复后补充风险评估
4. 提供手动触发评估功能

#### 8.2.2 审批系统异常
**场景**: 审核中心系统不可用
**处理方案**:
1. 提供离线审批申请功能
2. 系统恢复后自动同步审批数据
3. 紧急情况下提供临时放行机制
4. 记录所有异常处理过程

#### 8.2.3 大量商户风险状态变更
**场景**: 配置变更导致大量商户风险等级变化
**处理方案**:
1. 配置变更前进行影响评估
2. 提供分批生效机制
3. 支持配置回滚功能
4. 提供风险变更通知机制

### 8.3 系统异常处理

#### 8.3.1 性能异常
**场景**: 大量并发访问导致系统响应缓慢
**处理方案**:
1. 实施请求限流和熔断机制
2. 优化数据库查询和索引
3. 使用缓存减少数据库压力
4. 提供系统监控和告警

#### 8.3.2 数据一致性异常
**场景**: 分布式环境下数据不一致
**处理方案**:
1. 使用分布式事务保证数据一致性
2. 实施数据校验和修复机制
3. 提供数据同步工具
4. 定期进行数据一致性检查

## 9. 与现有系统集成

### 9.1 与审核中心集成

#### 9.1.1 集成架构

```mermaid
graph LR
    A[风险指标管理系统] --> B[业务操作拦截]
    B --> C{触发高风险?}
    C -->|是| D[生成审批申请]
    C -->|否| E[允许操作继续]

    D --> F[审核中心API]
    F --> G[审批流程引擎]
    G --> H[审批人员]

    H --> I{审批结果}
    I -->|通过| J[回调通知]
    I -->|拒绝| K[回调通知]

    J --> L[允许操作继续]
    K --> M[操作被拒绝]

    L --> N[记录审批日志]
    M --> N
```

#### 9.1.2 接口定义

**1. 创建审批申请接口**
```json
POST /audit-center/api/approval/create
{
  "applicationType": "RISK_OVERRIDE",
  "merchantId": "6026245559",
  "operationType": "MERCHANT_REGISTRATION",
  "riskIndicators": [
    {
      "indicatorCode": "REGISTRATION_COUNT",
      "indicatorName": "进件次数",
      "currentValue": 7,
      "riskLevel": "HIGH",
      "threshold": 3
    }
  ],
  "applicantId": "user123",
  "reason": "商户业务需要，申请特殊审批",
  "attachments": ["file1.pdf", "file2.jpg"]
}
```

**2. 审批结果回调接口**
```json
POST /risk-management/api/approval/callback
{
  "approvalId": "AP202401001",
  "merchantId": "6026245559",
  "operationType": "MERCHANT_REGISTRATION",
  "approvalStatus": "APPROVED",
  "approvalTime": "2024-01-15 14:30:00",
  "approver": "auditor001",
  "approvalComment": "经审核，同意该商户继续进件"
}
```

### 9.2 与商户评估系统集成

#### 9.2.1 数据同步机制

**实时同步**:
- 风险配置变更时，实时推送到评估系统
- 使用消息队列确保数据传输可靠性
- 支持增量同步和全量同步

**定时同步**:
- 每日凌晨进行全量数据校验
- 发现不一致时自动修复
- 生成同步报告

#### 9.2.2 评估结果缓存策略

**缓存层级**:
1. **Redis缓存**: 热点商户风险数据，过期时间1小时
2. **本地缓存**: 风险配置数据，过期时间30分钟
3. **数据库**: 完整的评估历史数据

**缓存更新策略**:
- 配置变更时清除相关缓存
- 评估结果变更时更新缓存
- 缓存穿透保护机制

### 9.3 与业务系统集成

#### 9.3.1 业务拦截点集成

**集成方式**: AOP切面拦截
**拦截点**:
- 商户进件申请
- 商户信息变更
- 大额交易处理
- 补贴申请审核

**集成代码示例**:
```java
@Aspect
@Component
public class RiskControlAspect {

    @Around("@annotation(RiskControl)")
    public Object riskControl(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取商户信息
        String merchantId = extractMerchantId(joinPoint);

        // 风险评估
        RiskAssessmentResult result = riskService.assess(merchantId);

        // 高风险拦截
        if (result.hasHighRisk()) {
            throw new BusinessBlockedException(result);
        }

        // 继续执行业务逻辑
        return joinPoint.proceed();
    }
}
```

#### 9.3.2 统一异常处理

**异常类型定义**:
```java
public class BusinessBlockedException extends RuntimeException {
    private String merchantId;
    private List<RiskIndicator> riskIndicators;
    private String operationType;

    // 构造函数和getter/setter
}
```

**全局异常处理器**:
```java
@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessBlockedException.class)
    public ResponseEntity<RiskBlockResponse> handleBusinessBlocked(
            BusinessBlockedException e) {

        RiskBlockResponse response = RiskBlockResponse.builder()
            .blocked(true)
            .merchantId(e.getMerchantId())
            .riskIndicators(e.getRiskIndicators())
            .operationType(e.getOperationType())
            .build();

        return ResponseEntity.ok(response);
    }
}
```

## 10. 实施计划

### 10.1 开发阶段划分

#### 第一阶段：基础功能开发 (4周)
- 风险指标库管理功能
- 基础数据表设计和创建
- 系统管理员操作界面
- 基本的CRUD操作

#### 第二阶段：配置功能开发 (3周)
- 省级机构配置功能
- 配置界面和交互逻辑
- 数据验证和权限控制
- 批量导入导出功能

#### 第三阶段：业务集成开发 (4周)
- 风险评估引擎集成
- 业务拦截机制实现
- 与审核中心对接
- 异常处理机制

#### 第四阶段：测试和优化 (3周)
- 功能测试和性能测试
- 安全测试和压力测试
- 用户体验优化
- 文档编写和培训

### 10.2 技术选型建议

**后端技术栈**:
- Spring Boot 2.7+
- MyBatis Plus 3.5+
- Redis 6.0+
- MySQL 8.0+
- RocketMQ 4.9+

**前端技术栈**:
- Vue 3.0+
- Element Plus
- Axios
- ECharts

**部署方案**:
- Docker容器化部署
- Nginx负载均衡
- 分布式部署架构

### 10.3 风险控制

**技术风险**:
- 大数据量下的性能问题
- 分布式环境下的数据一致性
- 与现有系统的兼容性

**业务风险**:
- 配置错误导致业务中断
- 风险评估不准确
- 用户操作复杂度过高

**缓解措施**:
- 充分的性能测试和优化
- 完善的回滚和恢复机制
- 详细的用户培训和文档

## 11. 总结

本PRD文档详细描述了风险指标管理与配置模块的功能需求、技术方案和实施计划。该模块将显著提升商户健康度监测系统的灵活性和可配置性，为各省级机构提供个性化的风险管控能力。

通过统一的风险指标管理、灵活的省级配置和智能的业务联动，系统将能够更好地适应不同地区的业务特点和风险管控要求，为邮政"微邮付"业务的健康发展提供有力支撑。
