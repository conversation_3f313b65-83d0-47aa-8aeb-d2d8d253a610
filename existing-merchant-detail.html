<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存量商户交易风险详情</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 0;
        }

        .page-container {
            padding: 24px;
        }

        .page-header {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: end;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-blue);
            text-decoration: none;
            font-size: 14px;
            padding: 8px 16px;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: var(--primary-blue-light);
        }

        .card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-body {
            padding: 24px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin: 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            margin-bottom: 24px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .info-value {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .risk-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .risk-high {
            background: #fff2f0;
            color: var(--error-red);
        }

        .risk-medium {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        .risk-low {
            background: #f6ffed;
            color: var(--success-green);
        }

        .risk-indicators {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
        }

        .indicator-card {
            background: #fafafa;
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
        }

        .indicator-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .indicator-table {
            width: 100%;
            border-collapse: collapse;
        }

        .indicator-table th,
        .indicator-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-light);
            font-size: 14px;
        }

        .indicator-table th {
            background: #f5f5f5;
            font-weight: 500;
            color: var(--text-primary);
        }

        .indicator-table td {
            color: var(--text-primary);
        }

        .indicator-value {
            font-weight: 600;
        }

        .indicator-value.high {
            color: var(--error-red);
        }

        .indicator-value.medium {
            color: var(--warning-orange);
        }

        .indicator-value.low {
            color: var(--success-green);
        }

        .evaluation-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
        }

        .evaluation-summary h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .summary-number1 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .summary-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .btn {
            height: 32px;
            padding: 4px 15px;
            border-radius: 6px;
            font-weight: 400;
            font-size: 14px;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-warning {
            background: var(--warning-orange);
            border-color: var(--warning-orange);
            color: #ffffff;
        }

        .btn-warning:hover {
            background: #d46b08;
            border-color: #d46b08;
        }

        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: #ffffff;
        }

        .btn-success:hover {
            background: #389e0d;
            border-color: #389e0d;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        .risk-explanation {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
        }

        .risk-explanation h4 {
            margin: 0 0 12px 0;
            color: var(--success-green);
            font-size: 16px;
            font-weight: 500;
        }

        .risk-explanation p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        .transaction-chart {
            background: var(--bg-container);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chart-placeholder {
            height: 300px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* 360度风险画像样式 */
        .risk-radar {
            position: relative;
            width: 100%;
            min-height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 40px 0;
        }

        .risk-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 10;
        }

        .merchant-avatar {
            width: 80px;
            height: 80px;
            background: var(--primary-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }

        .merchant-avatar i {
            font-size: 32px;
            color: white;
        }

        .merchant-info {
            background: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .merchant-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .merchant-id {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        /* 风险指标卡片 */
        .risk-card {
            position: absolute;
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color-light);
            transition: all 0.3s ease;
        }

        .risk-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        /* 修复特定卡片的悬停效果 - 确保水平居中不变 */
        .risk-card-5:hover {
            transform: translateY(-4px) !important;
            /* 只允许垂直移动 */
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
        }

        .risk-card-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-card-header i {
            font-size: 16px;
            color: var(--primary-blue);
        }

        .risk-card-header span:nth-child(2) {
            flex: 1;
            font-weight: 500;
            color: var(--text-primary);
        }

        .risk-card-content {
            padding: 16px;
        }

        .risk-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;
        }

        .risk-item:last-child {
            margin-bottom: 0;
        }

        .risk-label {
            color: var(--text-secondary);
            min-width: 70px;
            margin-right: 8px;
        }

        .risk-tag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .risk-tag.risk-high {
            background: #fff2f0;
            color: var(--error-red);
        }

        .risk-tag.risk-medium {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        .risk-tag.risk-low {
            background: #f6ffed;
            color: var(--success-green);
        }

        /* 风险卡片位置 - 只显示中高风险 */
        .risk-card-2 {
            top: 10%;
            left: 10%;
        }

        .risk-card-3 {
            top: 10%;
            right: 10%;
        }

        .risk-card-5 {
            bottom: 10%;
            left: calc(50% - 350px);
            /* 最终调整，确保完全不重叠 */
            transform: none !important;
        }

        /* 低风险指标底部排列 */
        .low-risk-section {
            margin-top: 40px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }

        .low-risk-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }

        .low-risk-card {
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--border-color-light);
            box-shadow: var(--shadow-card);
        }

        /* 新增：分区域风险展示样式 */
        .risk-section {
            margin-bottom: 32px;
        }

        .risk-section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            padding: 12px 16px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .risk-section-title.high-risk {
            background: #fff2f0;
            color: var(--error-red);
            border-left: 4px solid var(--error-red);
        }

        .risk-section-title.medium-risk {
            background: #fff7e6;
            color: var(--warning-orange);
            border-left: 4px solid var(--warning-orange);
        }

        .risk-section-title.low-risk {
            background: #f6ffed;
            color: var(--success-green);
            border-left: 4px solid var(--success-green);
        }

        .risk-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            max-width: 100%;
            gap: 20px;
        }

        /* 确保每行最多3个卡片 */
        @media (min-width: 1200px) {
            .risk-cards-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 768px) and (max-width: 1199px) {
            .risk-cards-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 767px) {
            .risk-cards-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 统一的风险卡片样式 */
        .unified-risk-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color-light);
            transition: all 0.3s ease;
            overflow: hidden;
            position: static !important;
            width: auto !important;
        }

        .unified-risk-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .unified-risk-card-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .unified-risk-card-header i {
            font-size: 16px;
        }

        .unified-risk-card-header .risk-title {
            flex: 1;
            font-weight: 500;
            color: var(--text-primary);
        }

        .unified-risk-card-content {
            padding: 16px;
        }

        .unified-risk-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;
        }

        .unified-risk-item:last-child {
            margin-bottom: 0;
        }

        .unified-risk-label {
            color: var(--text-secondary);
            min-width: 70px;
            margin-right: 8px;
        }

        /* 中风险卡片样式 */
        .unified-risk-card.medium-risk .unified-risk-card-header {
            background: #fff7e6;
        }

        .unified-risk-card.medium-risk .unified-risk-card-header i {
            color: var(--warning-orange);
        }

        .unified-risk-card.medium-risk .risk-tag {
            background: #fff7e6;
            color: var(--warning-orange);
        }

        /* 低风险卡片样式 */
        .unified-risk-card.low-risk .unified-risk-card-header {
            background: #f6ffed;
        }

        .unified-risk-card.low-risk .unified-risk-card-header i {
            color: var(--success-green);
        }

        .unified-risk-card.low-risk .risk-tag {
            background: #f6ffed;
            color: var(--success-green);
        }

        /* 风险指标提示功能样式 */
        .info-icon {
            color: #8c8c8c;
            cursor: pointer;
            margin-left: 8px;
            font-size: 14px;
            transition: color 0.3s ease;
            position: relative;
        }

        .info-icon:hover {
            color: var(--primary-blue);
        }

        .tooltip {
            position: absolute;
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 0;
            z-index: 1000;
            min-width: 280px;
            max-width: 400px;
            display: none;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            margin-top: 8px;
        }

        .tooltip.show {
            display: block;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #d9d9d9;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            top: -7px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #fff;
        }

        .tooltip-title {
            background: #f5f5f5;
            padding: 12px 16px;
            border-bottom: 1px solid #d9d9d9;
            font-weight: 500;
            color: var(--text-primary);
            border-radius: 8px 8px 0 0;
        }

        .tooltip-content {
            padding: 12px 16px;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* 中风险指标的提示图标颜色 */
        .medium-risk .info-icon {
            color: #ffb366;
        }

        .medium-risk .info-icon:hover {
            color: var(--warning-orange);
        }

        /* 低风险指标的提示图标颜色 */
        .low-risk .info-icon {
            color: #95de64;
        }

        .low-risk .info-icon:hover {
            color: var(--success-green);
        }
    </style>
</head>

<body>
    <div class="page-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <!-- <h1 class="page-title">存量商户风险详情</h1> -->
            <div style="display: flex; gap: 12px; align-items: center;">
                <button class="btn btn-success" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    导出评估报告
                </button>

            </div>
        </div>

        <!-- 评估概要 -->
        <div class="evaluation-summary">
            <h3><i class="fas fa-chart-line"></i> 风险评估概要</h3>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number">小李超市 <span class="summary-number1">(6026245559)</span></div>
                    <div class="summary-label">商户名称</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">中风险</div>
                    <div class="summary-label">综合风险等级</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">0个</div>
                    <div class="summary-label">高风险指标</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">3个</div>
                    <div class="summary-label">中风险指标</div>
                </div>
            </div>
        </div>

        <!-- 基础信息 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">基础信息</h3>
            </div>
            <div class="card-body">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">商户号</div>
                        <div class="info-value">6026245559</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">商户名称</div>
                        <div class="info-value">小李超市</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">商户负责人</div>
                        <div class="info-value">李明</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">负责人手机号</div>
                        <div class="info-value">13887635678</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">市级分公司</div>
                        <div class="info-value">北京分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">区县分公司</div>
                        <div class="info-value">海淀区分公司</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">营业所</div>
                        <div class="info-value">建国门营业所</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">评估月份</div>
                        <div class="info-value">2024-01</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">所属客户经理</div>
                        <div class="info-value">张三</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">联系方式</div>
                        <div class="info-value">13887635678</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 360度风险画像 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">商户风险画像</h3>

            </div>
            <div class="card-body">
                <!-- 中风险指标区域 -->
                <div class="risk-section">
                    <div class="risk-section-title medium-risk">
                        <i class="fas fa-exclamation-triangle"></i>
                        中风险指标
                    </div>
                    <div class="risk-cards-grid">
                        <div class="unified-risk-card medium-risk">
                            <div class="unified-risk-card-header" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-money-bill-wave"></i>
                                    <span class="risk-title">大额交易</span>
                                    <i class="fas fa-info-circle info-icon" onclick="toggleTooltip(this)"></i>
                                    <div class="tooltip">
                                        <div class="tooltip-title">大额交易风险指标说明</div>
                                        <div class="tooltip-content">
                                            <strong>判定标准：</strong><br>
                                            • 高风险：每日笔均大额交易金额 ≥ 5000元<br>
                                            • 中风险：每日笔均大额交易金额 3000-4999元<br>
                                            • 低风险：每日笔均大额交易金额 < 3000元<br><br>
                                            <strong>风险说明：</strong><br>
                                            大额交易频繁可能存在套现、洗钱等风险，需要重点监控交易行为的合理性。
                                        </div>
                                    </div>
                                </div>
                                <span class="risk-tag">中风险</span>
                            </div>
                            <div class="unified-risk-card-content">
                                <div class="unified-risk-item">
                                    <span class="unified-risk-label">判定原因：</span>
                                    <span>每日笔均大额交易金额：4000元</span>
                                </div>
                            </div>
                        </div>
                        <div class="unified-risk-card medium-risk">
                            <div class="unified-risk-card-header" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-network-wired"></i>
                                    <span class="risk-title">IP异常交易</span>
                                    <i class="fas fa-info-circle info-icon" onclick="toggleTooltip(this)"></i>
                                    <div class="tooltip">
                                        <div class="tooltip-title">IP异常交易风险指标说明</div>
                                        <div class="tooltip-content">
                                            <strong>判定标准：</strong><br>
                                            • 高风险：异地交易笔数 ≥ 10笔/日<br>
                                            • 中风险：异地交易笔数 5-9笔/日<br>
                                            • 低风险：异地交易笔数 < 5笔/日<br><br>
                                            <strong>风险说明：</strong><br>
                                            频繁的异地交易可能表明商户存在代理刷卡或移动POS机套现等违规行为。
                                        </div>
                                    </div>
                                </div>
                                <span class="risk-tag">中风险</span>
                            </div>
                            <div class="unified-risk-card-content">
                                <div class="unified-risk-item">
                                    <span class="unified-risk-label">判定原因：</span>
                                    <span>商户交易地址距常用地址2公里外交易笔数：7笔</span>
                                </div>
                            </div>
                        </div>
                        <div class="unified-risk-card medium-risk">
                            <div class="unified-risk-card-header" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-credit-card"></i>
                                    <span class="risk-title">信用卡交易</span>
                                    <i class="fas fa-info-circle info-icon" onclick="toggleTooltip(this)"></i>
                                    <div class="tooltip">
                                        <div class="tooltip-title">信用卡交易风险指标说明</div>
                                        <div class="tooltip-content">
                                            <strong>判定标准：</strong><br>
                                            • 高风险：信用卡交易占比 ≥ 80%<br>
                                            • 中风险：信用卡交易占比 60-79%<br>
                                            • 低风险：信用卡交易占比 < 60%<br><br>
                                            <strong>风险说明：</strong><br>
                                            过高的信用卡交易占比可能存在套现风险，需要关注交易的真实性和合理性。
                                        </div>
                                    </div>
                                </div>
                                <span class="risk-tag">中风险</span>
                            </div>
                            <div class="unified-risk-card-content">
                                <div class="unified-risk-item">
                                    <span class="unified-risk-label">判定原因：</span>
                                    <span>每日信用卡交易占比：65%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 低风险指标区域 -->
                <div class="risk-section">
                    <div class="risk-section-title low-risk">
                        <i class="fas fa-check-circle"></i>
                        低风险指标
                    </div>
                    <div class="risk-cards-grid">
                        <div class="unified-risk-card low-risk">
                            <div class="unified-risk-card-header" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-clock"></i>
                                    <span class="risk-title">交易时间</span>
                                    <i class="fas fa-info-circle info-icon" onclick="toggleTooltip(this)"></i>
                                    <div class="tooltip">
                                        <div class="tooltip-title">交易时间风险指标说明</div>
                                        <div class="tooltip-content">
                                            <strong>判定标准：</strong><br>
                                            • 高风险：深夜交易占比 ≥ 30%（22:00-06:00）<br>
                                            • 中风险：深夜交易占比 15-29%<br>
                                            • 低风险：深夜交易占比 < 15%<br><br>
                                            <strong>风险说明：</strong><br>
                                            正常的交易时间分布符合商户经营特点，异常时段交易过多可能存在风险。
                                        </div>
                                    </div>
                                </div>
                                <span class="risk-tag">低风险</span>
                            </div>
                            <div class="unified-risk-card-content">
                                <div class="unified-risk-item">
                                    <span class="unified-risk-label">判定原因：</span>
                                    <span>每日交易时间分布正常</span>
                                </div>
                            </div>
                        </div>
                        <div class="unified-risk-card low-risk">
                            <div class="unified-risk-card-header" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-users"></i>
                                    <span class="risk-title">重复交易人群</span>
                                    <i class="fas fa-info-circle info-icon" onclick="toggleTooltip(this)"></i>
                                    <div class="tooltip">
                                        <div class="tooltip-title">重复交易人群风险指标说明</div>
                                        <div class="tooltip-content">
                                            <strong>判定标准：</strong><br>
                                            • 高风险：重复交易者订单比例 ≥ 50%<br>
                                            • 中风险：重复交易者订单比例 30-49%<br>
                                            • 低风险：重复交易者订单比例 < 30%<br><br>
                                            <strong>风险说明：</strong><br>
                                            适度的重复交易比例表明商户有稳定的客户群体，过高可能存在刷单风险。
                                        </div>
                                    </div>
                                </div>
                                <span class="risk-tag">低风险</span>
                            </div>
                            <div class="unified-risk-card-content">
                                <div class="unified-risk-item">
                                    <span class="unified-risk-label">判定原因：</span>
                                    <span>每日重复交易者订单比例：15%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="unified-risk-card low-risk">
                            <div class="unified-risk-card-header" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span class="risk-title">优惠券使用</span>
                                    <i class="fas fa-info-circle info-icon" onclick="toggleTooltip(this)"></i>
                                    <div class="tooltip">
                                        <div class="tooltip-title">优惠券使用风险指标说明</div>
                                        <div class="tooltip-content">
                                            <strong>判定标准：</strong><br>
                                            • 高风险：优惠券使用占比 ≥ 80%<br>
                                            • 中风险：优惠券使用占比 60-79%<br>
                                            • 低风险：优惠券使用占比 < 60%<br><br>
                                            <strong>风险说明：</strong><br>
                                            合理的优惠券使用比例表明正常的营销活动，过高可能存在薅羊毛或虚假交易风险。
                                        </div>
                                    </div>
                                </div>
                                <span class="risk-tag">低风险</span>
                            </div>
                            <div class="unified-risk-card-content">
                                <div class="unified-risk-item">
                                    <span class="unified-risk-label">判定原因：</span>
                                    <span>每日优惠券使用占比：35%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    </div>


    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 风险指标提示功能
        function toggleTooltip(iconElement) {
            // 隐藏所有其他提示框
            hideAllTooltips();

            // 获取当前图标对应的提示框
            const tooltip = iconElement.nextElementSibling;
            if (tooltip && tooltip.classList.contains('tooltip')) {
                tooltip.classList.toggle('show');

                // 调整提示框位置，防止超出页面边界
                setTimeout(() => {
                    adjustTooltipPosition(tooltip);
                }, 10);
            }

            // 阻止事件冒泡
            event.stopPropagation();
        }

        function hideAllTooltips() {
            const tooltips = document.querySelectorAll('.tooltip');
            tooltips.forEach(tooltip => {
                tooltip.classList.remove('show');
            });
        }

        function adjustTooltipPosition(tooltip) {
            const rect = tooltip.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 如果提示框超出右边界，调整到左侧
            if (rect.right > viewportWidth - 20) {
                tooltip.style.left = 'auto';
                tooltip.style.right = '0';
                tooltip.style.transform = 'none';
            }

            // 如果提示框超出左边界，调整到右侧
            if (rect.left < 20) {
                tooltip.style.left = '0';
                tooltip.style.right = 'auto';
                tooltip.style.transform = 'none';
            }

            // 如果提示框超出底部边界，显示在上方
            if (rect.bottom > viewportHeight - 20) {
                tooltip.style.top = 'auto';
                tooltip.style.bottom = '100%';
                tooltip.style.marginTop = '0';
                tooltip.style.marginBottom = '8px';
            }
        }

        // 页面加载时获取商户ID
        document.addEventListener('DOMContentLoaded', function () {
            const merchantId = getUrlParameter('id');
            if (merchantId) {
                console.log('商户ID:', merchantId);
                // 这里可以根据商户ID加载具体数据
                loadMerchantData(merchantId);
            }

            // 添加全局点击事件监听器，点击其他区域时隐藏提示框
            document.addEventListener('click', function(event) {
                // 如果点击的不是提示图标或提示框内容，则隐藏所有提示框
                if (!event.target.closest('.info-icon') && !event.target.closest('.tooltip')) {
                    hideAllTooltips();
                }
            });

            // 阻止提示框内部点击事件冒泡
            document.addEventListener('click', function(event) {
                if (event.target.closest('.tooltip')) {
                    event.stopPropagation();
                }
            });
        });

        // 加载商户数据
        function loadMerchantData(merchantId) {
            // 模拟数据加载
            console.log('加载商户数据:', merchantId);
            // 这里可以调用API获取商户详细信息
        }

        // 下发整改任务
        function issueRectificationTask() {
            const merchantId = getUrlParameter('id') || 'M202301002';
            if (confirm('确定要为该商户下发整改任务吗？')) {
                alert(`正在为商户 ${merchantId} 下发整改任务...`);
                // 这里可以调用API下发整改任务
            }
        }

        // 下发走访任务
        function issueVisitTask() {
            const merchantId = getUrlParameter('id') || 'M202301002';
            if (confirm('确定要为该商户下发走访任务吗？')) {
                alert(`正在为商户 ${merchantId} 下发走访任务...`);
                // 这里可以调用API下发走访任务
            }
        }

        // 导出评估报告
        function exportReport() {
            const merchantId = getUrlParameter('id') || 'M202301002';
            alert(`正在导出商户 ${merchantId} 的风险评估报告...`);
            // 这里可以调用API导出报告
        }

        // 添加风险卡片悬停效果
        document.addEventListener('DOMContentLoaded', function () {
            const riskCards = document.querySelectorAll('.risk-card');
            riskCards.forEach(card => {
                card.addEventListener('mouseenter', function () {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function () {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>

</html>