<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险指标库 - 微邮付商户健康度监测系统</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1677ff;
            --primary-blue-hover: #4096ff;
            --primary-blue-active: #0958d9;
            --primary-blue-light: #e6f4ff;
            --success-green: #52c41a;
            --warning-orange: #fa8c16;
            --error-red: #ff4d4f;
            --bg-layout: #f5f5f5;
            --bg-container: #ffffff;
            --border-color: #d9d9d9;
            --border-color-light: #f0f0f0;
            --text-primary: #000000d9;
            --text-secondary: #00000073;
            --text-disabled: #00000040;
            --shadow-card: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
        }

        * {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, -apple-system, sans-serif;
        }

        body {
            background: var(--bg-layout);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            margin: 0;
            padding: 24px;
        }

        .page-header {
            background: var(--bg-container);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
        }

        .page-description {
            color: var(--text-secondary);
            margin: 0;
        }

        .content-card {
            background: var(--bg-container);
            border-radius: 8px;
            box-shadow: var(--shadow-card);
            border: 1px solid var(--border-color-light);
            overflow: hidden;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            background: var(--primary-blue-hover);
            border-color: var(--primary-blue-hover);
        }

        .btn-default {
            background: var(--bg-container);
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .btn-default:hover {
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .search-filters {
            padding: 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .form-label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .form-input, .form-select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-container);
            color: var(--text-primary);
            min-width: 120px;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        }

        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-light);
        }

        .data-table th {
            background: var(--bg-layout);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }

        .data-table td {
            color: var(--text-primary);
            font-size: 14px;
        }

        .data-table tbody tr:hover {
            background: var(--bg-layout);
        }

        .status-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-enabled {
            background: #f6ffed;
            color: var(--success-green);
            border: 1px solid #b7eb8f;
        }

        .status-disabled {
            background: #fff2f0;
            color: var(--error-red);
            border: 1px solid #ffccc7;
        }

        .category-tag {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            background: var(--primary-blue-light);
            color: var(--primary-blue);
            border: 1px solid #91caff;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        .btn-text {
            background: none;
            border: none;
            color: var(--primary-blue);
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
        }

        .btn-text:hover {
            background: var(--primary-blue-light);
        }

        .btn-text.danger {
            color: var(--error-red);
        }

        .btn-text.danger:hover {
            background: #fff2f0;
        }

        .pagination {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid var(--border-color-light);
        }

        .pagination-info {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            background: var(--bg-container);
            color: var(--text-primary);
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .page-btn:hover {
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .page-btn.active {
            background: var(--primary-blue);
            color: white;
            border-color: var(--primary-blue);
        }

        .page-btn:disabled {
            color: var(--text-disabled);
            cursor: not-allowed;
            border-color: var(--border-color-light);
        }

        .batch-actions {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border-color-light);
            display: none;
            background: var(--primary-blue-light);
            align-items: center;
            gap: 16px;
        }

        .batch-actions.show {
            display: flex;
        }

        .batch-info {
            color: var(--primary-blue);
            font-weight: 500;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">风险指标库</h1>
        <p class="page-description">统一管理所有类型的风险指标，定义判定规则和阈值标准</p>
    </div>

    <!-- 主要内容 -->
    <div class="content-card">
        <!-- 卡片头部 -->
        <div class="card-header">
            <h2 class="card-title">指标管理</h2>
            <div style="display: flex; gap: 12px;">
                <button class="btn btn-default" onclick="exportConfig()">
                    <i class="fas fa-download"></i>
                    导出配置
                </button>
                <button class="btn btn-default" onclick="importConfig()">
                    <i class="fas fa-upload"></i>
                    批量导入
                </button>
                <button class="btn btn-primary" onclick="createIndicator()">
                    <i class="fas fa-plus"></i>
                    新增指标
                </button>
            </div>
        </div>

        <!-- 搜索筛选区 -->
        <div class="search-filters">
            <div class="form-group">
                <label class="form-label">指标名称</label>
                <input type="text" class="form-input" placeholder="请输入指标名称" id="searchName">
            </div>
            <div class="form-group">
                <label class="form-label">分类</label>
                <select class="form-select" id="filterCategory">
                    <option value="">全部分类</option>
                    <option value="PROFILE">资料风险</option>
                    <option value="TRANSACTION">交易风险</option>
                    <option value="SUBSIDY">补贴风险</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">状态</label>
                <select class="form-select" id="filterStatus">
                    <option value="">全部状态</option>
                    <option value="1">启用</option>
                    <option value="0">禁用</option>
                </select>
            </div>
            <button class="btn btn-primary" onclick="searchIndicators()" style="margin-top: 20px;">
                <i class="fas fa-search"></i>
                搜索
            </button>
            <button class="btn btn-default" onclick="resetSearch()" style="margin-top: 20px;">
                重置
            </button>
        </div>

        <!-- 批量操作区 -->
        <div class="batch-actions" id="batchActions">
            <span class="batch-info">已选择 <span id="selectedCount">0</span> 项</span>
            <button class="btn btn-default btn-sm" onclick="batchEnable()">
                <i class="fas fa-check"></i>
                批量启用
            </button>
            <button class="btn btn-default btn-sm" onclick="batchDisable()">
                <i class="fas fa-times"></i>
                批量禁用
            </button>
            <button class="btn btn-default btn-sm danger" onclick="batchDelete()">
                <i class="fas fa-trash"></i>
                批量删除
            </button>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <input type="checkbox" class="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>指标名称</th>
                        <th>分类</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody id="indicatorTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination">
            <div class="pagination-info">
                共 <span id="totalCount">0</span> 条记录，每页显示 20 条
            </div>
            <div class="pagination-controls">
                <button class="page-btn" onclick="goToPage(1)" id="firstPage">首页</button>
                <button class="page-btn" onclick="goToPage(currentPage - 1)" id="prevPage">上一页</button>
                <span id="pageNumbers"></span>
                <button class="page-btn" onclick="goToPage(currentPage + 1)" id="nextPage">下一页</button>
                <button class="page-btn" onclick="goToPage(totalPages)" id="lastPage">末页</button>
            </div>
        </div>
    </div>
    <script>
        // 全局变量
        let currentPage = 1;
        let totalPages = 1;
        let totalCount = 0;
        let selectedItems = new Set();

        // 模拟数据
        const mockData = [
            {
                id: 1,
                indicatorCode: 'RISK_001',
                indicatorName: '进件次数',
                category: 'PROFILE',
                categoryName: '资料风险',
                status: 1,
                createdTime: '2024-01-15 10:30:00',
                updatedTime: '2024-01-20 14:20:00'
            },
            {
                id: 2,
                indicatorCode: 'RISK_002',
                indicatorName: '大额交易',
                category: 'TRANSACTION',
                categoryName: '交易风险',
                status: 1,
                createdTime: '2024-01-16 09:15:00',
                updatedTime: '2024-01-18 16:45:00'
            },
            {
                id: 3,
                indicatorCode: 'RISK_003',
                indicatorName: '补贴激增',
                category: 'SUBSIDY',
                categoryName: '补贴风险',
                status: 0,
                createdTime: '2024-01-17 11:20:00',
                updatedTime: '2024-01-19 13:30:00'
            },
            {
                id: 4,
                indicatorCode: 'RISK_004',
                indicatorName: '门头照重复',
                category: 'PROFILE',
                categoryName: '资料风险',
                status: 1,
                createdTime: '2024-01-18 14:10:00',
                updatedTime: '2024-01-21 09:25:00'
            },
            {
                id: 5,
                indicatorCode: 'RISK_005',
                indicatorName: 'IP异常交易',
                category: 'TRANSACTION',
                categoryName: '交易风险',
                status: 1,
                createdTime: '2024-01-19 16:40:00',
                updatedTime: '2024-01-22 11:15:00'
            }
        ];

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadIndicators();
        });

        // 加载指标数据
        function loadIndicators() {
            const searchName = document.getElementById('searchName').value;
            const filterCategory = document.getElementById('filterCategory').value;
            const filterStatus = document.getElementById('filterStatus').value;

            // 模拟筛选逻辑
            let filteredData = mockData.filter(item => {
                if (searchName && !item.indicatorName.includes(searchName)) return false;
                if (filterCategory && item.category !== filterCategory) return false;
                if (filterStatus && item.status.toString() !== filterStatus) return false;
                return true;
            });

            totalCount = filteredData.length;
            totalPages = Math.ceil(totalCount / 20);

            // 分页处理
            const startIndex = (currentPage - 1) * 20;
            const endIndex = startIndex + 20;
            const pageData = filteredData.slice(startIndex, endIndex);

            renderTable(pageData);
            updatePagination();
        }

        // 渲染表格
        function renderTable(data) {
            const tbody = document.getElementById('indicatorTableBody');
            tbody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <input type="checkbox" class="checkbox" value="${item.id}" onchange="toggleSelect(${item.id})">
                    </td>
                    <td>
                        <div style="font-weight: 500;">${item.indicatorName}</div>
                        <div style="font-size: 12px; color: var(--text-secondary);">${item.indicatorCode}</div>
                    </td>
                    <td>
                        <span class="category-tag">${item.categoryName}</span>
                    </td>
                    <td>
                        <span class="status-tag ${item.status ? 'status-enabled' : 'status-disabled'}">
                            ${item.status ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>${item.createdTime}</td>
                    <td>${item.updatedTime}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-text" onclick="editIndicator(${item.id})">编辑</button>
                            <button class="btn-text danger" onclick="deleteIndicator(${item.id})">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 更新总数显示
            document.getElementById('totalCount').textContent = totalCount;
        }

        // 更新分页
        function updatePagination() {
            const firstPage = document.getElementById('firstPage');
            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');
            const lastPage = document.getElementById('lastPage');
            const pageNumbers = document.getElementById('pageNumbers');

            // 更新按钮状态
            firstPage.disabled = currentPage === 1;
            prevPage.disabled = currentPage === 1;
            nextPage.disabled = currentPage === totalPages;
            lastPage.disabled = currentPage === totalPages;

            // 生成页码
            let pageNumbersHtml = '';
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                pageNumbersHtml += `
                    <button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `;
            }
            pageNumbers.innerHTML = pageNumbersHtml;
        }

        // 跳转页面
        function goToPage(page) {
            if (page < 1 || page > totalPages) return;
            currentPage = page;
            loadIndicators();
        }

        // 搜索指标
        function searchIndicators() {
            currentPage = 1;
            loadIndicators();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchName').value = '';
            document.getElementById('filterCategory').value = '';
            document.getElementById('filterStatus').value = '';
            currentPage = 1;
            loadIndicators();
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                if (selectAll.checked) {
                    selectedItems.add(parseInt(checkbox.value));
                } else {
                    selectedItems.delete(parseInt(checkbox.value));
                }
            });

            updateBatchActions();
        }

        // 切换单个选择
        function toggleSelect(id) {
            if (selectedItems.has(id)) {
                selectedItems.delete(id);
            } else {
                selectedItems.add(id);
            }
            updateBatchActions();
        }

        // 更新批量操作区域
        function updateBatchActions() {
            const batchActions = document.getElementById('batchActions');
            const selectedCount = document.getElementById('selectedCount');

            selectedCount.textContent = selectedItems.size;

            if (selectedItems.size > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }
        }

        // 新增指标
        function createIndicator() {
            window.open('risk-indicator-form.html', '_blank', 'width=800,height=600');
        }

        // 编辑指标
        function editIndicator(id) {
            window.open(`risk-indicator-form.html?id=${id}`, '_blank', 'width=800,height=600');
        }

        // 删除指标
        function deleteIndicator(id) {
            if (confirm('确定要删除这个风险指标吗？删除后不可恢复。')) {
                // 模拟删除操作
                console.log('删除指标:', id);
                alert('删除成功');
                loadIndicators();
            }
        }

        // 批量启用
        function batchEnable() {
            if (selectedItems.size === 0) return;
            if (confirm(`确定要启用选中的 ${selectedItems.size} 个指标吗？`)) {
                console.log('批量启用:', Array.from(selectedItems));
                alert('批量启用成功');
                selectedItems.clear();
                loadIndicators();
                updateBatchActions();
            }
        }

        // 批量禁用
        function batchDisable() {
            if (selectedItems.size === 0) return;
            if (confirm(`确定要禁用选中的 ${selectedItems.size} 个指标吗？`)) {
                console.log('批量禁用:', Array.from(selectedItems));
                alert('批量禁用成功');
                selectedItems.clear();
                loadIndicators();
                updateBatchActions();
            }
        }

        // 批量删除
        function batchDelete() {
            if (selectedItems.size === 0) return;
            if (confirm(`确定要删除选中的 ${selectedItems.size} 个指标吗？删除后不可恢复。`)) {
                console.log('批量删除:', Array.from(selectedItems));
                alert('批量删除成功');
                selectedItems.clear();
                loadIndicators();
                updateBatchActions();
            }
        }

        // 导出配置
        function exportConfig() {
            console.log('导出配置');
            alert('配置导出功能开发中...');
        }

        // 批量导入
        function importConfig() {
            console.log('批量导入');
            alert('批量导入功能开发中...');
        }
    </script>
</body>
</html>
